odoo.define('hr_dashboard_fixed.dashboard', function (require) {
    "use strict";

    var AbstractAction = require('web.AbstractAction');
    var core = require('web.core');
    var QWeb = core.qweb;
    var rpc = require('web.rpc');
    var session = require('web.session');
    var field_utils = require('web.field_utils');
    var time = require('web.time');

    // Load the Chart.js library
    var Chart = window.Chart;

    var HrDashboard = AbstractAction.extend({
        template: 'hr_dashboard_fixed.dashboard',
        events: {
            'click .o_hr_dashboard_card_footer a': '_onCardFooterClick',
        },

        /**
         * @override
         */
        init: function (parent, context) {
            this._super(parent, context);
            this.dashboardData = {};
        },

        /**
         * @override
         */
        willStart: function () {
            var self = this;
            console.log("Dashboard willStart - fetching data");

            // First call super to initialize the widget
            return this._super().then(function () {
                // Fetch dashboard data without updating UI (will be done in start)
                return self._fetchDashboardData(false).catch(function(error) {
                    console.error("Error in willStart:", error);
                    // We'll handle the error in the catch block of _fetchDashboardData
                    // Just return a resolved promise to continue initialization
                    return Promise.resolve();
                });
            });
        },

        /**
         * @override
         */
        start: function () {
            var self = this;

            // First call super to initialize the widget
            return this._super().then(function () {
                console.log("Dashboard start - initializing with demo data");

                // Initialize with demo data first
                self.dashboardData = {
                    employee_count: 26,
                    department_count: 5,
                    new_employees: 3,
                    employees_per_department: [
                        {'id': 1, 'name': 'إدارة الموارد البشرية', 'employee_count': 5, 'color': '#FF6384'},
                        {'id': 2, 'name': 'إدارة التطوير', 'employee_count': 8, 'color': '#36A2EB'},
                        {'id': 3, 'name': 'إدارة المبيعات', 'employee_count': 6, 'color': '#FFCE56'},
                        {'id': 4, 'name': 'إدارة التسويق', 'employee_count': 4, 'color': '#4BC0C0'},
                        {'id': 5, 'name': 'الإدارة العامة', 'employee_count': 3, 'color': '#9966FF'}
                    ],
                    employees_by_gender: {
                        male: 14,
                        female: 12,
                        other: 0
                    }
                };

                // Render with demo data immediately
                self._renderDashboard();

                // Then try to fetch real data in the background
                self._fetchDashboardData(false).then(function() {
                    console.log("Real data fetched, re-rendering");
                    self._renderDashboard();
                }).catch(function(error) {
                    console.warn("Could not fetch real data, keeping demo data:", error);
                });

            }).guardedCatch(function(error) {
                console.error("Error in dashboard start:", error);
                // Show error message to user
                self.$el.html('<div class="alert alert-danger text-center">' +
                    '<h3><i class="fa fa-exclamation-triangle"></i> خطأ في تحميل لوحة المعلومات</h3>' +
                    '<p>يرجى تحديث الصفحة أو الاتصال بمسؤول النظام.</p>' +
                    '<p>التفاصيل التقنية: ' + (error.message || error) + '</p>' +
                    '</div>');
            });
        },

        /**
         * Fetch the dashboard data from the server
         * @private
         * @param {Boolean} updateUI - Whether to update the UI with loading/error messages
         * @returns {Promise}
         */
        _fetchDashboardData: function (updateUI) {
            var self = this;

            // Default to true if not specified
            updateUI = (updateUI !== false);

            // Show loading message if we should update UI and $el is available
            if (updateUI && this.$el) {
                this.$el.html('<div class="text-center p-5">' +
                    '<i class="fa fa-spinner fa-spin fa-3x"></i>' +
                    '<p class="mt-2">جاري تحميل لوحة المعلومات...</p>' +
                    '</div>');
            }

            return rpc.query({
                route: '/hr_dashboard_fixed/dashboard_data',
                params: {}
            }).then(function (data) {
                console.log("Dashboard data received:", data);

                // Check if data is valid
                if (!data) {
                    console.error("No data received from server");
                    if (updateUI && self.$el) {
                        self.$el.html('<div class="alert alert-danger text-center">' +
                            '<h3><i class="fa fa-exclamation-triangle"></i> خطأ في البيانات</h3>' +
                            '<p>لم يتم استلام أي بيانات من الخادم</p>' +
                            '</div>');
                    }
                    return Promise.reject("No data received");
                }

                // Check if there's an error in the data
                if (data.error) {
                    console.error("Error in dashboard data:", data.error);
                    if (updateUI && self.$el) {
                        self.$el.html('<div class="alert alert-danger text-center">' +
                            '<h3><i class="fa fa-exclamation-triangle"></i> خطأ في البيانات</h3>' +
                            '<p>' + data.error + '</p>' +
                            '</div>');
                    }
                    return Promise.reject(data.error);
                }

                self.dashboardData = data;

                // Set default values if data is empty or undefined
                if (!self.dashboardData.employee_count) self.dashboardData.employee_count = 0;
                if (!self.dashboardData.department_count) self.dashboardData.department_count = 0;
                if (!self.dashboardData.new_employees) self.dashboardData.new_employees = 0;
                if (!self.dashboardData.employees_per_department || self.dashboardData.employees_per_department.length === 0) {
                    console.log("No department data found, using demo data");
                    self.dashboardData.employees_per_department = [
                        {'id': 1, 'name': 'إدارة الموارد البشرية', 'employee_count': 5, 'color': '#FF6384'},
                        {'id': 2, 'name': 'إدارة التطوير', 'employee_count': 8, 'color': '#36A2EB'},
                        {'id': 3, 'name': 'إدارة المبيعات', 'employee_count': 6, 'color': '#FFCE56'},
                        {'id': 4, 'name': 'إدارة التسويق', 'employee_count': 4, 'color': '#4BC0C0'},
                        {'id': 5, 'name': 'الإدارة العامة', 'employee_count': 3, 'color': '#9966FF'}
                    ];
                    self.dashboardData.employee_count = 26;
                    self.dashboardData.department_count = 5;
                }
                if (!self.dashboardData.employees_by_gender) {
                    self.dashboardData.employees_by_gender = {
                        male: 14,
                        female: 12,
                        other: 0
                    };
                }

                // Update UI if requested and element exists
                if (updateUI && self.$el) {
                    self._updateDashboard();
                }

                return self.dashboardData;
            }).catch(function(error) {
                console.error("Error fetching dashboard data:", error);

                // Initialize with demo data on error
                self.dashboardData = {
                    employee_count: 26,
                    department_count: 5,
                    new_employees: 3,
                    employees_per_department: [
                        {'id': 1, 'name': 'إدارة الموارد البشرية', 'employee_count': 5, 'color': '#FF6384'},
                        {'id': 2, 'name': 'إدارة التطوير', 'employee_count': 8, 'color': '#36A2EB'},
                        {'id': 3, 'name': 'إدارة المبيعات', 'employee_count': 6, 'color': '#FFCE56'},
                        {'id': 4, 'name': 'إدارة التسويق', 'employee_count': 4, 'color': '#4BC0C0'},
                        {'id': 5, 'name': 'الإدارة العامة', 'employee_count': 3, 'color': '#9966FF'}
                    ],
                    employees_by_gender: {
                        male: 14,
                        female: 12,
                        other: 0
                    }
                };

                // Update UI with demo data if requested and element exists
                if (updateUI && self.$el) {
                    self._updateDashboard();
                }

                return Promise.reject(error);
            });
        },

        /**
         * Update the dashboard UI
         * @private
         */
        _updateDashboard: function () {
            this._renderDashboard();
        },

        /**
         * Render the dashboard
         * @private
         */
        _renderDashboard: function () {
            var self = this;

            // Check if we have data
            if (!this.dashboardData || Object.keys(this.dashboardData).length === 0) {
                console.error("No dashboard data available");
                this.$el.html('<div class="alert alert-warning text-center">' +
                    '<h3><i class="fa fa-exclamation-circle"></i> لا توجد بيانات متاحة</h3>' +
                    '<p>لم يتم العثور على بيانات لعرضها في لوحة المعلومات.</p>' +
                    '</div>');
                return;
            }

            // Check if there's an error
            if (this.dashboardData.error) {
                console.error("Dashboard data error:", this.dashboardData.error);
                this.$el.html('<div class="alert alert-danger text-center">' +
                    '<h3><i class="fa fa-exclamation-triangle"></i> خطأ في البيانات</h3>' +
                    '<p>' + this.dashboardData.error + '</p>' +
                    '</div>');
                return;
            }

            console.log("Rendering dashboard with data:", this.dashboardData);

            try {
                // Clear the element first
                this.$el.empty();

                // Create a container for the dashboard
                var $dashboard = $('<div class="o_hr_dashboard"></div>');
                this.$el.append($dashboard);

                // Render the template
                var html = QWeb.render('hr_dashboard_fixed_template', {
                    employee_count: this.dashboardData.employee_count,
                    department_count: this.dashboardData.department_count,
                    employees_per_department: this.dashboardData.employees_per_department,
                    new_employees: this.dashboardData.new_employees,
                    employees_by_gender: this.dashboardData.employees_by_gender,
                    round: Math.round,
                    Math: Math, // Make Math available in the template
                });

                // Append the rendered template
                $dashboard.append(html);

                console.log("Dashboard template rendered successfully");

                // Use setTimeout to ensure DOM is fully rendered before accessing canvas elements
                setTimeout(function() {
                    console.log("Rendering charts...");
                    console.log("Available canvas elements:", self.$('canvas').length);
                    console.log("Department chart canvas:", self.$('#employees_per_department_chart').length);
                    self._renderCharts();
                }, 1000); // Increased timeout to ensure DOM is fully rendered
            } catch (error) {
                console.error("Error rendering dashboard:", error);
                this.$el.html('<div class="alert alert-danger text-center">' +
                    '<h3><i class="fa fa-exclamation-triangle"></i> خطأ في عرض لوحة المعلومات</h3>' +
                    '<p>حدث خطأ أثناء عرض لوحة المعلومات. يرجى المحاولة مرة أخرى لاحقًا.</p>' +
                    '<p>التفاصيل التقنية: ' + (error.message || error) + '</p>' +
                    '</div>');
            }
        },

        /**
         * Render the charts
         * @private
         */
        _renderCharts: function () {
            try {
                console.log("Starting to render charts");

                // Render department pie chart
                if (!this.dashboardData.employees_per_department ||
                    this.dashboardData.employees_per_department.length === 0) {
                    console.warn('No department data for charts');
                    this.$('#employees_per_department_chart').closest('.o_hr_dashboard_chart_container')
                        .html('<div class="alert alert-info">لا توجد بيانات للإدارات لعرضها في المخطط البياني</div>');
                } else {
                    this._renderEmployeesPerDepartmentPieChart();
                }

                // Render gender chart
                if (!this.dashboardData.employees_by_gender) {
                    console.warn('No gender data for charts');
                    this.$('#employees_by_gender_chart').closest('.o_hr_dashboard_chart_container')
                        .html('<div class="alert alert-info">لا توجد بيانات للجنس لعرضها في المخطط البياني</div>');
                } else {
                    this._renderEmployeesByGenderChart();
                }

                console.log("Charts rendering completed");
            } catch (error) {
                console.error('Error rendering charts:', error);
                // Show error message in chart containers
                this.$('.o_hr_dashboard_chart_container').each(function() {
                    $(this).html('<div class="alert alert-danger">' +
                        '<i class="fa fa-exclamation-triangle"></i> خطأ في عرض المخطط البياني' +
                        '</div>');
                });
            }
        },

        /**
         * Render the employees per department pie chart using CSS and SVG
         * @private
         */
        _renderEmployeesPerDepartmentPieChart: function () {
            var self = this;

            if (!this.dashboardData.employees_per_department ||
                this.dashboardData.employees_per_department.length === 0) {
                this.$('#employees_per_department_chart').html(
                    '<div class="alert alert-info text-center">لا توجد بيانات للإدارات لعرضها</div>'
                );
                return;
            }

            var data = this.dashboardData.employees_per_department;
            var total = data.reduce(function(sum, dept) {
                return sum + dept.employee_count;
            }, 0);

            if (total === 0) {
                this.$('#employees_per_department_chart').html(
                    '<div class="alert alert-info text-center">لا توجد موظفين في الإدارات</div>'
                );
                return;
            }

            // Create SVG pie chart
            var svgSize = 280;
            var radius = 100;
            var centerX = svgSize / 2;
            var centerY = svgSize / 2;

            var html = '<div class="pie-chart-container" style="text-align: center;">';
            html += '<svg width="' + svgSize + '" height="' + svgSize + '" style="margin: 20px auto;">';

            var currentAngle = 0;

            data.forEach(function(dept, index) {
                var percentage = (dept.employee_count / total) * 100;
                var angle = (dept.employee_count / total) * 360;

                // Calculate path for pie slice
                var startAngle = currentAngle * Math.PI / 180;
                var endAngle = (currentAngle + angle) * Math.PI / 180;

                var x1 = centerX + radius * Math.cos(startAngle);
                var y1 = centerY + radius * Math.sin(startAngle);
                var x2 = centerX + radius * Math.cos(endAngle);
                var y2 = centerY + radius * Math.sin(endAngle);

                var largeArcFlag = angle > 180 ? 1 : 0;

                var pathData = [
                    'M', centerX, centerY,
                    'L', x1, y1,
                    'A', radius, radius, 0, largeArcFlag, 1, x2, y2,
                    'Z'
                ].join(' ');

                html += '<path d="' + pathData + '" fill="' + dept.color + '" stroke="#fff" stroke-width="2" opacity="0.8">';
                html += '<title>' + dept.name + ': ' + dept.employee_count + ' موظف (' + percentage.toFixed(1) + '%)</title>';
                html += '</path>';

                currentAngle += angle;
            });

            html += '</svg>';

            // Add legend
            html += '<div class="pie-chart-legend" style="margin-top: 20px;">';
            data.forEach(function(dept) {
                var percentage = ((dept.employee_count / total) * 100).toFixed(1);
                html += '<div class="legend-item" style="display: inline-block; margin: 5px 15px; text-align: center;">';
                html += '<div style="width: 20px; height: 20px; background-color: ' + dept.color + '; display: inline-block; margin-left: 8px; border-radius: 3px;"></div>';
                html += '<span style="font-size: 12px; font-weight: bold;">' + dept.name + '</span><br>';
                html += '<span style="font-size: 11px; color: #666;">' + dept.employee_count + ' موظف (' + percentage + '%)</span>';
                html += '</div>';
            });
            html += '</div>';

            html += '</div>';

            this.$('#employees_per_department_chart').html(html);
        },



        /**
         * Render the employees by gender chart using CSS bars
         * @private
         */
        _renderEmployeesByGenderChart: function () {
            var self = this;

            // Check if we have data to display
            if (!this.dashboardData.employees_by_gender) {
                console.warn('No gender data available for chart');
                return;
            }

            var maleCount = this.dashboardData.employees_by_gender.male || 0;
            var femaleCount = this.dashboardData.employees_by_gender.female || 0;
            var otherCount = this.dashboardData.employees_by_gender.other || 0;

            var totalCount = maleCount + femaleCount + otherCount;

            if (totalCount === 0) {
                this.$('#employees_by_gender_chart').closest('.o_hr_dashboard_chart_container')
                    .html('<div class="alert alert-info text-center">لا توجد بيانات للموظفين لعرضها</div>');
                return;
            }

            var chartHtml = '<div class="simple-gender-chart">';
            chartHtml += '<h5>توزيع الموظفين حسب الجنس</h5>';

            // Male bar
            var malePercentage = totalCount > 0 ? (maleCount / totalCount) * 100 : 0;
            chartHtml += '<div class="chart-item" style="margin-bottom: 15px;">';
            chartHtml += '<div class="label" style="margin-bottom: 5px; font-weight: bold;">ذكر (' + maleCount + ' موظف)</div>';
            chartHtml += '<div class="bar" style="background-color: #1f77b4; height: 25px; width: ' + malePercentage + '%; border-radius: 3px; position: relative;">';
            chartHtml += '<span style="position: absolute; right: 5px; top: 50%; transform: translateY(-50%); color: white; font-weight: bold;">' + maleCount + '</span>';
            chartHtml += '</div>';
            chartHtml += '</div>';

            // Female bar
            var femalePercentage = totalCount > 0 ? (femaleCount / totalCount) * 100 : 0;
            chartHtml += '<div class="chart-item" style="margin-bottom: 15px;">';
            chartHtml += '<div class="label" style="margin-bottom: 5px; font-weight: bold;">أنثى (' + femaleCount + ' موظف)</div>';
            chartHtml += '<div class="bar" style="background-color: #e377c2; height: 25px; width: ' + femalePercentage + '%; border-radius: 3px; position: relative;">';
            chartHtml += '<span style="position: absolute; right: 5px; top: 50%; transform: translateY(-50%); color: white; font-weight: bold;">' + femaleCount + '</span>';
            chartHtml += '</div>';
            chartHtml += '</div>';

            // Other bar (only if there are other gender employees)
            if (otherCount > 0) {
                var otherPercentage = (otherCount / totalCount) * 100;
                chartHtml += '<div class="chart-item" style="margin-bottom: 15px;">';
                chartHtml += '<div class="label" style="margin-bottom: 5px; font-weight: bold;">أخرى (' + otherCount + ' موظف)</div>';
                chartHtml += '<div class="bar" style="background-color: #7f7f7f; height: 25px; width: ' + otherPercentage + '%; border-radius: 3px; position: relative;">';
                chartHtml += '<span style="position: absolute; right: 5px; top: 50%; transform: translateY(-50%); color: white; font-weight: bold;">' + otherCount + '</span>';
                chartHtml += '</div>';
                chartHtml += '</div>';
            }

            chartHtml += '</div>';

            // Replace the canvas with our custom chart
            this.$('#employees_by_gender_chart').closest('.o_hr_dashboard_chart_container').html(chartHtml);
        },

        /**
         * Handle click on card footer
         * @private
         * @param {MouseEvent} ev
         */
        _onCardFooterClick: function (ev) {
            ev.preventDefault();
            var action = $(ev.currentTarget).data('action');
            this._openAction(action);
        },

        /**
         * Open the action
         * @private
         * @param {String} action
         */
        _openAction: function (action) {
            switch (action) {
                case 'employees':
                    this.do_action({
                        name: 'Employees',
                        res_model: 'hr.employee',
                        views: [[false, 'list'], [false, 'form']],
                        type: 'ir.actions.act_window',
                        view_mode: 'list,form',
                    });
                    break;
                case 'departments':
                    this.do_action({
                        name: 'Departments',
                        res_model: 'hr.department',
                        views: [[false, 'list'], [false, 'form']],
                        type: 'ir.actions.act_window',
                        view_mode: 'list,form',
                    });
                    break;
                case 'new_employees':
                    var date_from = moment(new Date()).subtract(30, 'days').format('YYYY-MM-DD');
                    this.do_action({
                        name: 'New Employees',
                        res_model: 'hr.employee',
                        domain: [['create_date', '>=', date_from]],
                        views: [[false, 'list'], [false, 'form']],
                        type: 'ir.actions.act_window',
                        view_mode: 'list,form',
                    });
                    break;
            }
        },
    });

    core.action_registry.add('hr_dashboard_fixed', HrDashboard);

    return HrDashboard;
});
