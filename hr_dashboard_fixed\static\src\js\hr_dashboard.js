odoo.define('hr_dashboard_fixed.dashboard', function (require) {
    "use strict";

    var AbstractAction = require('web.AbstractAction');
    var core = require('web.core');
    var QWeb = core.qweb;
    var rpc = require('web.rpc');
    var session = require('web.session');
    var field_utils = require('web.field_utils');
    var time = require('web.time');

    // Load the Chart.js library
    var Chart = window.Chart;

    var HrDashboard = AbstractAction.extend({
        template: 'hr_dashboard_fixed.dashboard',
        events: {
            'click .o_hr_dashboard_card_footer a': '_onCardFooterClick',
        },

        /**
         * @override
         */
        init: function (parent, context) {
            this._super(parent, context);
            this.dashboardData = {};
        },

        /**
         * @override
         */
        willStart: function () {
            var self = this;
            console.log("Dashboard willStart - fetching data");

            // First call super to initialize the widget
            return this._super().then(function () {
                // Fetch dashboard data without updating UI (will be done in start)
                return self._fetchDashboardData(false).catch(function(error) {
                    console.error("Error in willStart:", error);
                    // We'll handle the error in the catch block of _fetchDashboardData
                    // Just return a resolved promise to continue initialization
                    return Promise.resolve();
                });
            });
        },

        /**
         * @override
         */
        start: function () {
            var self = this;

            // First call super to initialize the widget
            return this._super().then(function () {
                console.log("Dashboard start - initializing with demo data");

                // Initialize with demo data first
                self.dashboardData = {
                    employee_count: 26,
                    department_count: 5,
                    new_employees: 3,
                    employees_per_department: [
                        {'id': 1, 'name': 'إدارة الموارد البشرية', 'employee_count': 5, 'color': '#FF6384'},
                        {'id': 2, 'name': 'إدارة التطوير', 'employee_count': 8, 'color': '#36A2EB'},
                        {'id': 3, 'name': 'إدارة المبيعات', 'employee_count': 6, 'color': '#FFCE56'},
                        {'id': 4, 'name': 'إدارة التسويق', 'employee_count': 4, 'color': '#4BC0C0'},
                        {'id': 5, 'name': 'الإدارة العامة', 'employee_count': 3, 'color': '#9966FF'}
                    ],
                    employees_by_gender: {
                        male: 14,
                        female: 12,
                        other: 0
                    }
                };

                // Render with demo data immediately
                self._renderDashboard();

                // Then try to fetch real data in the background
                self._fetchDashboardData(false).then(function() {
                    console.log("Real data fetched, re-rendering");
                    self._renderDashboard();
                }).catch(function(error) {
                    console.warn("Could not fetch real data, keeping demo data:", error);
                });

            }).guardedCatch(function(error) {
                console.error("Error in dashboard start:", error);
                // Show error message to user
                self.$el.html('<div class="alert alert-danger text-center">' +
                    '<h3><i class="fa fa-exclamation-triangle"></i> خطأ في تحميل لوحة المعلومات</h3>' +
                    '<p>يرجى تحديث الصفحة أو الاتصال بمسؤول النظام.</p>' +
                    '<p>التفاصيل التقنية: ' + (error.message || error) + '</p>' +
                    '</div>');
            });
        },

        /**
         * Fetch the dashboard data from the server
         * @private
         * @param {Boolean} updateUI - Whether to update the UI with loading/error messages
         * @returns {Promise}
         */
        _fetchDashboardData: function (updateUI) {
            var self = this;

            // Default to true if not specified
            updateUI = (updateUI !== false);

            // Show loading message if we should update UI and $el is available
            if (updateUI && this.$el) {
                this.$el.html('<div class="text-center p-5">' +
                    '<i class="fa fa-spinner fa-spin fa-3x"></i>' +
                    '<p class="mt-2">جاري تحميل لوحة المعلومات...</p>' +
                    '</div>');
            }

            return rpc.query({
                route: '/hr_dashboard_fixed/dashboard_data',
                params: {}
            }).then(function (data) {
                console.log("Dashboard data received:", data);

                // Check if data is valid
                if (!data) {
                    console.error("No data received from server");
                    if (updateUI && self.$el) {
                        self.$el.html('<div class="alert alert-danger text-center">' +
                            '<h3><i class="fa fa-exclamation-triangle"></i> خطأ في البيانات</h3>' +
                            '<p>لم يتم استلام أي بيانات من الخادم</p>' +
                            '</div>');
                    }
                    return Promise.reject("No data received");
                }

                // Check if there's an error in the data
                if (data.error) {
                    console.error("Error in dashboard data:", data.error);
                    if (updateUI && self.$el) {
                        self.$el.html('<div class="alert alert-danger text-center">' +
                            '<h3><i class="fa fa-exclamation-triangle"></i> خطأ في البيانات</h3>' +
                            '<p>' + data.error + '</p>' +
                            '</div>');
                    }
                    return Promise.reject(data.error);
                }

                self.dashboardData = data;

                // Set default values if data is empty or undefined
                if (!self.dashboardData.employee_count) self.dashboardData.employee_count = 0;
                if (!self.dashboardData.department_count) self.dashboardData.department_count = 0;
                if (!self.dashboardData.new_employees) self.dashboardData.new_employees = 0;
                if (!self.dashboardData.employees_per_department || self.dashboardData.employees_per_department.length === 0) {
                    console.log("No department data found, using demo data");
                    self.dashboardData.employees_per_department = [
                        {'id': 1, 'name': 'إدارة الموارد البشرية', 'employee_count': 5, 'color': '#FF6384'},
                        {'id': 2, 'name': 'إدارة التطوير', 'employee_count': 8, 'color': '#36A2EB'},
                        {'id': 3, 'name': 'إدارة المبيعات', 'employee_count': 6, 'color': '#FFCE56'},
                        {'id': 4, 'name': 'إدارة التسويق', 'employee_count': 4, 'color': '#4BC0C0'},
                        {'id': 5, 'name': 'الإدارة العامة', 'employee_count': 3, 'color': '#9966FF'}
                    ];
                    self.dashboardData.employee_count = 26;
                    self.dashboardData.department_count = 5;
                }
                if (!self.dashboardData.employees_by_gender) {
                    self.dashboardData.employees_by_gender = {
                        male: 14,
                        female: 12,
                        other: 0
                    };
                }

                // Update UI if requested and element exists
                if (updateUI && self.$el) {
                    self._updateDashboard();
                }

                return self.dashboardData;
            }).catch(function(error) {
                console.error("Error fetching dashboard data:", error);

                // Initialize with demo data on error
                self.dashboardData = {
                    employee_count: 26,
                    department_count: 5,
                    new_employees: 3,
                    employees_per_department: [
                        {'id': 1, 'name': 'إدارة الموارد البشرية', 'employee_count': 5, 'color': '#FF6384'},
                        {'id': 2, 'name': 'إدارة التطوير', 'employee_count': 8, 'color': '#36A2EB'},
                        {'id': 3, 'name': 'إدارة المبيعات', 'employee_count': 6, 'color': '#FFCE56'},
                        {'id': 4, 'name': 'إدارة التسويق', 'employee_count': 4, 'color': '#4BC0C0'},
                        {'id': 5, 'name': 'الإدارة العامة', 'employee_count': 3, 'color': '#9966FF'}
                    ],
                    employees_by_gender: {
                        male: 14,
                        female: 12,
                        other: 0
                    }
                };

                // Update UI with demo data if requested and element exists
                if (updateUI && self.$el) {
                    self._updateDashboard();
                }

                return Promise.reject(error);
            });
        },

        /**
         * Update the dashboard UI
         * @private
         */
        _updateDashboard: function () {
            this._renderDashboard();
        },

        /**
         * Render the dashboard
         * @private
         */
        _renderDashboard: function () {
            var self = this;

            // Check if we have data
            if (!this.dashboardData || Object.keys(this.dashboardData).length === 0) {
                console.error("No dashboard data available");
                this.$el.html('<div class="alert alert-warning text-center">' +
                    '<h3><i class="fa fa-exclamation-circle"></i> لا توجد بيانات متاحة</h3>' +
                    '<p>لم يتم العثور على بيانات لعرضها في لوحة المعلومات.</p>' +
                    '</div>');
                return;
            }

            // Check if there's an error
            if (this.dashboardData.error) {
                console.error("Dashboard data error:", this.dashboardData.error);
                this.$el.html('<div class="alert alert-danger text-center">' +
                    '<h3><i class="fa fa-exclamation-triangle"></i> خطأ في البيانات</h3>' +
                    '<p>' + this.dashboardData.error + '</p>' +
                    '</div>');
                return;
            }

            console.log("Rendering dashboard with data:", this.dashboardData);

            try {
                // Clear the element first
                this.$el.empty();

                // Create a container for the dashboard
                var $dashboard = $('<div class="o_hr_dashboard"></div>');
                this.$el.append($dashboard);

                // Render the template
                var html = QWeb.render('hr_dashboard_fixed_template', {
                    employee_count: this.dashboardData.employee_count,
                    department_count: this.dashboardData.department_count,
                    employees_per_department: this.dashboardData.employees_per_department,
                    new_employees: this.dashboardData.new_employees,
                    employees_by_gender: this.dashboardData.employees_by_gender,
                    round: Math.round,
                    Math: Math, // Make Math available in the template
                });

                // Append the rendered template
                $dashboard.append(html);

                console.log("Dashboard template rendered successfully");

                // Use setTimeout to ensure DOM is fully rendered before accessing canvas elements
                setTimeout(function() {
                    console.log("Rendering charts...");
                    console.log("Available canvas elements:", self.$('canvas').length);
                    console.log("Department chart canvas:", self.$('#employees_per_department_chart').length);
                    self._renderCharts();
                }, 1000); // Increased timeout to ensure DOM is fully rendered
            } catch (error) {
                console.error("Error rendering dashboard:", error);
                this.$el.html('<div class="alert alert-danger text-center">' +
                    '<h3><i class="fa fa-exclamation-triangle"></i> خطأ في عرض لوحة المعلومات</h3>' +
                    '<p>حدث خطأ أثناء عرض لوحة المعلومات. يرجى المحاولة مرة أخرى لاحقًا.</p>' +
                    '<p>التفاصيل التقنية: ' + (error.message || error) + '</p>' +
                    '</div>');
            }
        },

        /**
         * Render the charts
         * @private
         */
        _renderCharts: function () {
            try {
                console.log("Starting to render charts");

                // Render department pie chart
                if (!this.dashboardData.employees_per_department ||
                    this.dashboardData.employees_per_department.length === 0) {
                    console.warn('No department data for charts');
                    this.$('#employees_per_department_chart').closest('.o_hr_dashboard_chart_container')
                        .html('<div class="alert alert-info">لا توجد بيانات للإدارات لعرضها في المخطط البياني</div>');
                } else {
                    this._renderEmployeesPerDepartmentPieChart();
                }

                // Render gender chart
                if (!this.dashboardData.employees_by_gender) {
                    console.warn('No gender data for charts');
                    this.$('#employees_by_gender_chart').closest('.o_hr_dashboard_chart_container')
                        .html('<div class="alert alert-info">لا توجد بيانات للجنس لعرضها في المخطط البياني</div>');
                } else {
                    this._renderEmployeesByGenderChart();
                }

                console.log("Charts rendering completed");
            } catch (error) {
                console.error('Error rendering charts:', error);
                // Show error message in chart containers
                this.$('.o_hr_dashboard_chart_container').each(function() {
                    $(this).html('<div class="alert alert-danger">' +
                        '<i class="fa fa-exclamation-triangle"></i> خطأ في عرض المخطط البياني' +
                        '</div>');
                });
            }
        },

        /**
         * Render the employees per department mixed chart (bars + line) using SVG
         * @private
         */
        _renderEmployeesPerDepartmentPieChart: function () {
            var self = this;

            if (!this.dashboardData.employees_per_department ||
                this.dashboardData.employees_per_department.length === 0) {
                this.$('#employees_per_department_chart').html(
                    '<div class="alert alert-info text-center">لا توجد بيانات للإدارات لعرضها</div>'
                );
                return;
            }

            var data = this.dashboardData.employees_per_department;
            var total = data.reduce(function(sum, dept) {
                return sum + dept.employee_count;
            }, 0);

            if (total === 0) {
                this.$('#employees_per_department_chart').html(
                    '<div class="alert alert-info text-center">لا توجد موظفين في الإدارات</div>'
                );
                return;
            }

            // Chart dimensions
            var chartWidth = 400;
            var chartHeight = 300;
            var margin = {top: 40, right: 30, bottom: 60, left: 60};
            var plotWidth = chartWidth - margin.left - margin.right;
            var plotHeight = chartHeight - margin.top - margin.bottom;

            // Find max value for scaling
            var maxEmployees = Math.max.apply(Math, data.map(function(d) { return d.employee_count; }));
            var maxPercentage = Math.max.apply(Math, data.map(function(d) { return (d.employee_count / total) * 100; }));

            var html = '<div class="mixed-chart-container" style="text-align: center; padding: 20px;">';

            // Title
            html += '<h4 style="margin-bottom: 10px; color: #666;">توزيع الموظفين حسب الإدارات</h4>';
            html += '<p style="font-size: 12px; color: #999; margin-bottom: 20px;">(آخر 12 شهر)</p>';

            // Legend
            html += '<div style="margin-bottom: 15px;">';
            html += '<span style="display: inline-block; margin: 0 15px;">';
            html += '<span style="display: inline-block; width: 15px; height: 15px; background-color: #87CEEB; margin-left: 5px;"></span>';
            html += '<span style="font-size: 12px;">عدد الموظفين</span>';
            html += '</span>';
            html += '<span style="display: inline-block; margin: 0 15px;">';
            html += '<span style="display: inline-block; width: 15px; height: 2px; background-color: #333; margin-left: 5px;"></span>';
            html += '<span style="font-size: 12px;">النسبة المئوية</span>';
            html += '</span>';
            html += '</div>';

            // SVG Chart
            html += '<svg width="' + chartWidth + '" height="' + chartHeight + '" style="border: 1px solid #eee; background: #fafafa;">';

            // Y-axis (left) - Employee count
            for (var i = 0; i <= 5; i++) {
                var yValue = (maxEmployees / 5) * i;
                var yPos = margin.top + plotHeight - (i * plotHeight / 5);

                html += '<text x="' + (margin.left - 10) + '" y="' + (yPos + 4) + '" text-anchor="end" font-size="10" fill="#666">';
                html += Math.round(yValue) + '</text>';

                // Grid lines
                if (i > 0) {
                    html += '<line x1="' + margin.left + '" y1="' + yPos + '" x2="' + (margin.left + plotWidth) + '" y2="' + yPos + '" stroke="#e0e0e0" stroke-width="1"></line>';
                }
            }

            // Y-axis (right) - Percentage
            for (var i = 0; i <= 5; i++) {
                var yValue = (maxPercentage / 5) * i;
                var yPos = margin.top + plotHeight - (i * plotHeight / 5);

                html += '<text x="' + (margin.left + plotWidth + 10) + '" y="' + (yPos + 4) + '" text-anchor="start" font-size="10" fill="#666">';
                html += Math.round(yValue) + '%</text>';
            }

            // X-axis labels and bars
            var barWidth = plotWidth / data.length * 0.6;
            var barSpacing = plotWidth / data.length;

            // Line points for percentage
            var linePoints = [];

            data.forEach(function(dept, index) {
                var xPos = margin.left + (index * barSpacing) + (barSpacing / 2);
                var barHeight = (dept.employee_count / maxEmployees) * plotHeight;
                var barY = margin.top + plotHeight - barHeight;

                // Bar
                html += '<rect x="' + (xPos - barWidth/2) + '" y="' + barY + '" width="' + barWidth + '" height="' + barHeight + '" fill="#87CEEB" stroke="#5DADE2" stroke-width="1" opacity="0.8">';
                html += '<title>' + dept.name + ': ' + dept.employee_count + ' موظف</title>';
                html += '</rect>';

                // X-axis label
                html += '<text x="' + xPos + '" y="' + (margin.top + plotHeight + 15) + '" text-anchor="middle" font-size="9" fill="#666">';
                html += dept.name.length > 10 ? dept.name.substring(0, 10) + '...' : dept.name;
                html += '</text>';

                // Line point for percentage
                var percentage = (dept.employee_count / total) * 100;
                var lineY = margin.top + plotHeight - (percentage / maxPercentage) * plotHeight;
                linePoints.push({x: xPos, y: lineY, percentage: percentage});
            });

            // Draw line for percentage
            if (linePoints.length > 1) {
                var pathData = 'M ' + linePoints[0].x + ' ' + linePoints[0].y;
                for (var i = 1; i < linePoints.length; i++) {
                    pathData += ' L ' + linePoints[i].x + ' ' + linePoints[i].y;
                }
                html += '<path d="' + pathData + '" stroke="#333" stroke-width="2" fill="none"></path>';

                // Line points
                linePoints.forEach(function(point) {
                    html += '<circle cx="' + point.x + '" cy="' + point.y + '" r="4" fill="#333" stroke="#fff" stroke-width="2">';
                    html += '<title>النسبة: ' + point.percentage.toFixed(1) + '%</title>';
                    html += '</circle>';
                });
            }

            // Axis lines
            html += '<line x1="' + margin.left + '" y1="' + margin.top + '" x2="' + margin.left + '" y2="' + (margin.top + plotHeight) + '" stroke="#333" stroke-width="2"></line>';
            html += '<line x1="' + margin.left + '" y1="' + (margin.top + plotHeight) + '" x2="' + (margin.left + plotWidth) + '" y2="' + (margin.top + plotHeight) + '" stroke="#333" stroke-width="2"></line>';
            html += '<line x1="' + (margin.left + plotWidth) + '" y1="' + margin.top + '" x2="' + (margin.left + plotWidth) + '" y2="' + (margin.top + plotHeight) + '" stroke="#333" stroke-width="2"></line>';

            // Y-axis labels
            html += '<text x="' + (margin.left - 35) + '" y="' + (margin.top + plotHeight/2) + '" text-anchor="middle" font-size="11" fill="#666" transform="rotate(-90 ' + (margin.left - 35) + ' ' + (margin.top + plotHeight/2) + ')">عدد الموظفين</text>';
            html += '<text x="' + (margin.left + plotWidth + 35) + '" y="' + (margin.top + plotHeight/2) + '" text-anchor="middle" font-size="11" fill="#666" transform="rotate(90 ' + (margin.left + plotWidth + 35) + ' ' + (margin.top + plotHeight/2) + ')">النسبة المئوية</text>';

            html += '</svg>';

            // Summary box (like in the image)
            html += '<div style="margin-top: 15px; padding: 10px; background: rgba(0,0,0,0.8); color: white; border-radius: 5px; display: inline-block;">';
            html += '<div style="font-size: 12px; font-weight: bold;">إجمالي الموظفين: ' + total + '</div>';
            html += '<div style="font-size: 11px;">متوسط الموظفين لكل إدارة: ' + (total / data.length).toFixed(1) + '</div>';
            html += '</div>';

            html += '</div>';

            this.$('#employees_per_department_chart').html(html);
        },



        /**
         * Render the employees by gender chart using CSS bars
         * @private
         */
        _renderEmployeesByGenderChart: function () {
            var self = this;

            // Check if we have data to display
            if (!this.dashboardData.employees_by_gender) {
                console.warn('No gender data available for chart');
                return;
            }

            var maleCount = this.dashboardData.employees_by_gender.male || 0;
            var femaleCount = this.dashboardData.employees_by_gender.female || 0;
            var otherCount = this.dashboardData.employees_by_gender.other || 0;

            var totalCount = maleCount + femaleCount + otherCount;

            if (totalCount === 0) {
                this.$('#employees_by_gender_chart').closest('.o_hr_dashboard_chart_container')
                    .html('<div class="alert alert-info text-center">لا توجد بيانات للموظفين لعرضها</div>');
                return;
            }

            var chartHtml = '<div class="simple-gender-chart">';
            chartHtml += '<h5>توزيع الموظفين حسب الجنس</h5>';

            // Male bar
            var malePercentage = totalCount > 0 ? (maleCount / totalCount) * 100 : 0;
            chartHtml += '<div class="chart-item" style="margin-bottom: 15px;">';
            chartHtml += '<div class="label" style="margin-bottom: 5px; font-weight: bold;">ذكر (' + maleCount + ' موظف)</div>';
            chartHtml += '<div class="bar" style="background-color: #1f77b4; height: 25px; width: ' + malePercentage + '%; border-radius: 3px; position: relative;">';
            chartHtml += '<span style="position: absolute; right: 5px; top: 50%; transform: translateY(-50%); color: white; font-weight: bold;">' + maleCount + '</span>';
            chartHtml += '</div>';
            chartHtml += '</div>';

            // Female bar
            var femalePercentage = totalCount > 0 ? (femaleCount / totalCount) * 100 : 0;
            chartHtml += '<div class="chart-item" style="margin-bottom: 15px;">';
            chartHtml += '<div class="label" style="margin-bottom: 5px; font-weight: bold;">أنثى (' + femaleCount + ' موظف)</div>';
            chartHtml += '<div class="bar" style="background-color: #e377c2; height: 25px; width: ' + femalePercentage + '%; border-radius: 3px; position: relative;">';
            chartHtml += '<span style="position: absolute; right: 5px; top: 50%; transform: translateY(-50%); color: white; font-weight: bold;">' + femaleCount + '</span>';
            chartHtml += '</div>';
            chartHtml += '</div>';

            // Other bar (only if there are other gender employees)
            if (otherCount > 0) {
                var otherPercentage = (otherCount / totalCount) * 100;
                chartHtml += '<div class="chart-item" style="margin-bottom: 15px;">';
                chartHtml += '<div class="label" style="margin-bottom: 5px; font-weight: bold;">أخرى (' + otherCount + ' موظف)</div>';
                chartHtml += '<div class="bar" style="background-color: #7f7f7f; height: 25px; width: ' + otherPercentage + '%; border-radius: 3px; position: relative;">';
                chartHtml += '<span style="position: absolute; right: 5px; top: 50%; transform: translateY(-50%); color: white; font-weight: bold;">' + otherCount + '</span>';
                chartHtml += '</div>';
                chartHtml += '</div>';
            }

            chartHtml += '</div>';

            // Replace the canvas with our custom chart
            this.$('#employees_by_gender_chart').closest('.o_hr_dashboard_chart_container').html(chartHtml);
        },

        /**
         * Handle click on card footer
         * @private
         * @param {MouseEvent} ev
         */
        _onCardFooterClick: function (ev) {
            ev.preventDefault();
            var action = $(ev.currentTarget).data('action');
            this._openAction(action);
        },

        /**
         * Open the action
         * @private
         * @param {String} action
         */
        _openAction: function (action) {
            switch (action) {
                case 'employees':
                    this.do_action({
                        name: 'Employees',
                        res_model: 'hr.employee',
                        views: [[false, 'list'], [false, 'form']],
                        type: 'ir.actions.act_window',
                        view_mode: 'list,form',
                    });
                    break;
                case 'departments':
                    this.do_action({
                        name: 'Departments',
                        res_model: 'hr.department',
                        views: [[false, 'list'], [false, 'form']],
                        type: 'ir.actions.act_window',
                        view_mode: 'list,form',
                    });
                    break;
                case 'new_employees':
                    var date_from = moment(new Date()).subtract(30, 'days').format('YYYY-MM-DD');
                    this.do_action({
                        name: 'New Employees',
                        res_model: 'hr.employee',
                        domain: [['create_date', '>=', date_from]],
                        views: [[false, 'list'], [false, 'form']],
                        type: 'ir.actions.act_window',
                        view_mode: 'list,form',
                    });
                    break;
            }
        },
    });

    core.action_registry.add('hr_dashboard_fixed', HrDashboard);

    return HrDashboard;
});
