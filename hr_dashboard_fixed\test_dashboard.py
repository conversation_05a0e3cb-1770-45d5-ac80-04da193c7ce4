# -*- coding: utf-8 -*-

"""
Test script to verify the dashboard is working correctly
"""

import logging
from odoo import http
from odoo.http import request

_logger = logging.getLogger(__name__)

class TestDashboard(http.Controller):
    
    @http.route('/hr_dashboard_fixed/test', type='http', auth='user')
    def test_dashboard(self):
        """
        Test endpoint to verify dashboard functionality
        """
        try:
            dashboard = request.env['hr.dashboard'].sudo()
            data = dashboard.get_dashboard_data()
            
            html = """
            <html>
            <head>
                <title>HR Dashboard Test</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    .success { color: green; }
                    .error { color: red; }
                    .data { background: #f5f5f5; padding: 10px; margin: 10px 0; }
                </style>
            </head>
            <body>
                <h1>HR Dashboard Test Results</h1>
                <div class="success">✓ Dashboard controller is working</div>
                <div class="success">✓ Dashboard model is accessible</div>
                
                <h2>Dashboard Data:</h2>
                <div class="data">
                    <strong>Employee Count:</strong> %s<br>
                    <strong>Department Count:</strong> %s<br>
                    <strong>New Employees:</strong> %s<br>
                    <strong>Departments:</strong><br>
                    %s
                </div>
                
                <h2>Test Links:</h2>
                <a href="/web#action=hr_dashboard_fixed.hr_dashboard_action">Open Dashboard</a><br>
                <a href="/hr_dashboard_fixed/dashboard_data">Test JSON API</a>
            </body>
            </html>
            """ % (
                data.get('employee_count', 'N/A'),
                data.get('department_count', 'N/A'), 
                data.get('new_employees', 'N/A'),
                '<br>'.join([f"- {dept.get('name', 'Unknown')}: {dept.get('employee_count', 0)} employees" 
                           for dept in data.get('employees_per_department', [])])
            )
            
            return html
            
        except Exception as e:
            _logger.error("Test failed: %s", e)
            return f"""
            <html>
            <body>
                <h1>HR Dashboard Test - ERROR</h1>
                <div style="color: red;">
                    <strong>Error:</strong> {str(e)}
                </div>
            </body>
            </html>
            """
