<?xml version="1.0" encoding="utf-8"?>
<odoo>
<template id="report_journal">
    <t t-call="web.html_container">
        <t t-set="data_report_margin_top" t-value="12"/>
        <t t-set="data_report_header_spacing" t-value="9"/>
        <t t-set="data_report_dpi" t-value="110"/>
        <t t-foreach="docs" t-as="o">
            <t t-if="lines[o.id]">
                <t t-if="not o" t-set="o" t-value="doc"/>

                <t t-if="not company">
                    <!-- Multicompany -->
                    <t t-if="company_id">
                        <t t-set="company" t-value="company_id"/>
                    </t>
                    <t t-elif="o and 'company_id' in o and o.company_id.sudo()">
                        <t t-set="company" t-value="o.company_id.sudo()"/>
                    </t>
                    <t t-else="else">
                        <t t-set="company" t-value="res_company"/>
                    </t>
                </t>

                <div class="header">
                    <div class="row">
                        <div class="col-2">
                            <span t-esc="context_timestamp(datetime.datetime.now()).strftime('%Y-%m-%d %H:%M')"/>
                        </div>
                        <div class="col-8 text-center">
                            <span t-esc="company.name"/> - <span t-esc="o.name"/>
                             (<span t-if="data['form']['target_move'] == 'all'">Posted and Draft Entries</span><span t-if="data['form']['target_move'] == 'posted'">Posted Entries only</span>)
                        </div>
                        <div class="col-2 text-right">
                            <ul class="list-inline">
                                <li class="list-inline-item"><span class="page"/></li>
                                <li class="list-inline-item">/</li>
                                <li class="list-inline-item"><span class="topage"/></li>
                            </ul>
                        </div>
                    </div>
                    <t t-if="data['form'].get('date_from') or data['form'].get('date_to')">
                        <div class="row">
                            <div class="col-8 offset-2 text-center">
                                <t t-if="data['form'].get('date_from')">
                                    From : <span t-esc="data['form'].get('date_from')"/>
                                </t>
                                <t t-if="data['form'].get('date_from') and data['form'].get('date_to')">
                                    -
                                </t>
                                <t t-if="data['form'].get('date_to')">
                                    To : <span t-esc="data['form'].get('date_to')"/>
                                </t>
                            </div>
                        </div>
                    </t>
                </div>
                <div class="article" t-att-data-oe-model="o and o._name" t-att-data-oe-id="o and o.id" t-att-data-oe-lang="o and o.env.context.get('lang')">
                    <div class="page">
                    <h2><t t-esc="o.name"/> Journal<span t-if="data['form']['target_move'] == 'all'"> - Posted and Draft Entries</span><span t-if="data['form']['target_move'] == 'posted'"> - Posted Entries Only</span></h2>
                    <table class="table table-sm table-striped mt-4" id="move_table">
                        <thead>
                            <tr>
                                <th t-if="data['form'].get('sort_selection') == 'move_name'">Move</th>
                                <th t-if="data['form'].get('sort_selection') == 'move_name'">Date</th>
                                <th t-if="data['form'].get('sort_selection') == 'date'">Date</th>
                                <th t-if="data['form'].get('sort_selection') == 'date'">Move</th>
                                <!-- Set a minimum width for columns, otherwise the line breaks too early -->
                                <th style="min-width: 150px">Partner</th>
                                <th style="min-width: 250px">Account</th>
                                <th style="min-width: 350px">Label</th>
                                <th t-if="data['form']['amount_currency']" style="text-align: right;">Currency</th>
                                <th style="text-align: right;">Debit</th>
                                <th style="text-align: right;">Credit</th>
                            </tr>
                        </thead>
                        <tbody>
                            <t t-set="last_am" t-value=""/>
                            <t t-set="last_date" t-value=""/>
                            <t t-set="last_partner" t-value=""/>
                            <tr t-foreach="lines[o.id]" t-as="aml">
                                <t t-if="data['form'].get('sort_selection') == 'move_name'">
                                    <t t-set="am_name" t-value="aml.move_id.name != '/' and aml.move_id.name or ('*'+str(aml.move_id.id))"/>
                                    <t t-if="last_am == am_name">
                                        <td/>
                                    </t>
                                    <t t-else="">
                                        <td><span t-esc="am_name" class="font-weight-bold"/></td>
                                        <t t-set="last_am" t-value="am_name"/>
                                        <!-- Reset these when changing move -->
                                        <t t-set="last_date" t-value=""/>
                                        <t t-set="last_partner" t-value=""/>
                                    </t>
                                    <t t-if="last_date == aml.date">
                                        <td/>
                                    </t>
                                    <t t-else="">
                                        <td><span t-field="aml.date"/></td>
                                        <t t-set="last_date" t-value="aml.date"/>
                                    </t>
                                </t>
                                <t t-else="">
                                    <t t-set="am_name" t-value="aml.move_id.name != '/' and aml.move_id.name or ('*'+str(aml.move_id.id))"/>
                                    <t t-if="last_date == aml.date and last_am == am_name">
                                        <td/>
                                    </t>
                                    <t t-else="">
                                        <td><span t-field="aml.date" class="font-weight-bold"/></td>
                                        <t t-set="last_date" t-value="aml.date"/>
                                        <!-- Reset these when changing data -->
                                        <t t-set="last_am" t-value=""/>
                                        <t t-set="last_partner" t-value=""/>
                                    </t>
                                    <t t-if="last_am == am_name">
                                        <td/>
                                    </t>
                                    <t t-else="">
                                        <td><span t-esc="am_name"/></td>
                                        <t t-set="last_am" t-value="am_name"/>
                                        <t t-set="last_partner" t-value=""/>
                                    </t>
                                </t>
                                <t t-set="partner_name" t-value="aml.sudo().partner_id and aml.sudo().partner_id.name and aml.sudo().partner_id.name or ''"/>
                                <!-- general journals always show partners, as we can have multiple != ones in a single move -->
                                <t t-if="last_partner == partner_name and o.type != 'general'">
                                    <td/>
                                </t>
                                <t t-else="">
                                    <td><span t-esc="partner_name"/></td>
                                    <t t-set="last_partner" t-value="partner_name"/>
                                </t>
                                <td><span t-field="aml.account_id.code"/> <span t-field="aml.account_id.name"/></td>
                                <td><span t-esc="aml.name"/></td>
                                <td t-if="data['form']['amount_currency'] and aml.amount_currency" class="pull-right">
                                    <span class="text-monospace"
                                          t-esc="aml.amount_currency"
                                          t-options="{'widget': 'monetary', 'display_currency': aml.currency_id}"/>
                                </td>
                                <td style="text-align: right;">
                                    <span t-if="not (company_id or res_company).currency_id.is_zero(aml.debit)"
                                          t-esc="aml.debit"
                                          t-options="{
                                              'widget': 'monetary',
                                              'display_currency': (company_id or res_company).currency_id
                                          }"
                                          class="text-monospace"/>
                                </td>
                                <td style="text-align: right;">
                                    <span t-if="not (company_id or res_company).currency_id.is_zero(aml.credit)"
                                          t-esc="aml.credit"
                                          t-options="{
                                              'widget': 'monetary',
                                              'display_currency': (company_id or res_company).currency_id
                                          }"
                                          class="text-monospace"/>
                                </td>
                            </tr>
                            <tr class="bg-white">
                                <td/>
                                <td/>
                                <td/>
                                <td/>
                                <td class="text-right"><strong>Total</strong></td>
                                <td style="text-align: right;">
                                    <span t-esc="sum_debit(data, o)"
                                          t-options="{
                                            'widget': 'monetary',
                                            'display_currency': (company_id or res_company).currency_id
                                          }"
                                          class="font-weight-bold text-monospace"/>
                                </td>
                                <td style="text-align: right;">
                                    <span t-esc="sum_credit(data, o)"
                                          t-options="{
                                              'widget': 'monetary',
                                              'display_currency': (company_id or res_company).currency_id
                                          }"
                                          class="font-weight-bold text-monospace"/>

                                </td>
                                <td t-if="data['form']['amount_currency']"/>
                            </tr>
                        </tbody>
                    </table>

                    <div class="row" style="page-break-inside: avoid" id="tax_section">
                        <t t-set="taxes" t-value="get_taxes(data, o)"/>
                        <div class="col-5" t-if="taxes">
                            <table class="table table-sm table-striped" id="tax_table">
                                <thead>
                                    <tr><th colspan="3">Taxes Applied</th></tr>
                                    <tr>
                                        <th>Name</th>
                                        <th style="text-align: right;">Base Amount</th>
                                        <th style="text-align: right;">Tax Amount</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr t-foreach="taxes" t-as="tax">
                                        <td><span t-esc="tax.name"/></td>
                                        <td style="text-align: right;">
                                            <span t-esc="taxes[tax]['base_amount']"
                                                  t-options="{
                                                      'widget': 'monetary',
                                                      'display_currency': (company_id or res_company).currency_id
                                                  }"
                                                  class="text-monospace"/>
                                        </td>
                                        <td style="text-align: right;">
                                            <span t-esc="taxes[tax]['tax_amount']"
                                                  t-options="{
                                                      'widget': 'monetary',
                                                      'display_currency': (company_id or res_company).currency_id
                                                  }"
                                                  class="text-monospace"/>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    </div>
                </div>
            </t>
        </t>
    </t>
</template>
</odoo>
