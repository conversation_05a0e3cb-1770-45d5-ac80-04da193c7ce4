<?xml version="1.0" encoding="UTF-8"?>

<templates xml:space="preserve">

    <t t-name="ShowPaymentInfo">
        <div>
            <t t-if="outstanding">
                <div>
                    <strong class="float-left" id="outstanding" t-esc="title"/>
                </div>
            </t>
            <table style="width:100%;">
                <t t-foreach="lines" t-as="line">
                    <tr>
                    <t t-if="outstanding">
                        <td>
                            <a title="assign to invoice"
                               role="button"
                               class="oe_form_field btn btn-secondary outstanding_credit_assign"
                               t-att-data-id="line.id"
                               style="margin-right: 0px; padding-left: 5px; padding-right: 5px;"
                               href="#"
                               data-toggle="tooltip">Add</a>
                        </td>
                        <td style="max-width: 11em;">
                            <a t-att-title="line.date"
                               role="button"
                               class="oe_form_field btn btn-link open_account_move"
                               t-att-move-id="line.move_id"
                               style="margin-right: 5px; text-overflow: ellipsis; overflow: hidden; white-space: nowrap; padding-left: 0px; width:100%; text-align:left;"
                               data-toggle="tooltip"
                               t-att-payment-id="account_payment_id"
                               t-esc="line.journal_name"/>
                        </td>
                    </t>
                    <t t-if="!outstanding">
                        <td>
                           <a role="button" tabindex="0" class="js_payment_info fa fa-info-circle" t-att-index="line.index" style="margin-right:5px;" aria-label="Info" title="Journal Entry Info" data-toggle="tooltip"></a>
                        </td>
                        <td>
                            <i class="o_field_widget text-right o_payment_label">Paid on <t t-esc="line.date"></t></i>
                        </td>
                    </t>
                        <td style="text-align:right;">
                            <span class="oe_form_field oe_form_field_float oe_form_field_monetary" style="margin-left: -10px;">
                                <t t-if="line.position === 'before'">
                                    <t t-esc="line.currency"/>
                                </t>
                                <t t-esc="line.amount"></t>
                                <t t-if="line.position === 'after'">
                                    <t t-esc="line.currency"/>
                                </t>
                            </span>
                        </td>
                    </tr>
                </t>
            </table>
        </div>
    </t>

    <t t-name="PaymentPopOver">
        <div>
            <table>
                <tr>
                    <td><strong>Amount: </strong></td>
                    <td>
                        <t t-if="position === 'before'">
                            <t t-esc="currency"/>
                        </t>
                        <t t-esc="amount"></t>
                        <t t-if="position === 'after'">
                            <t t-esc="currency"/>
                        </t>
                    </td>
                </tr>
                 <tr>
                    <td><strong>Memo: </strong></td>
                    <td>
                        <div style="width: 200px; word-wrap: break-word">
                            <t t-esc="ref"/>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td><strong>Date: </strong></td>
                    <td><t t-esc="date"/></td>
                </tr>
                <tr>
                    <td><strong>Journal: </strong></td>
                    <td><t t-esc="journal_name"/><span t-if="payment_method_name"> (<t t-esc="payment_method_name"/>)</span></td>
                </tr>
            </table>
        </div>
        <button class="btn btn-sm btn-primary js_unreconcile_payment float-left" t-att-partial-id="partial_id" t-att-payment-id="payment_id" t-att-move-id="move_id" style="margin-top:5px; margin-bottom:5px;" groups="account.group_account_invoice">Unreconcile</button>
        <button class="btn btn-sm btn-secondary js_open_payment float-right" t-att-payment-id="account_payment_id" t-att-move-id="move_id" style="margin-top:5px; margin-bottom:5px;">View</button>
    </t>

</templates>
