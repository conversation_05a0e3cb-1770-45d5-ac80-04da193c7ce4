<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="view_account_type_search" model="ir.ui.view">
            <field name="name">account.account.type.search</field>
            <field name="model">account.account.type</field>
            <field name="arch" type="xml">
                <search string="Account Type">
                    <field name="name" filter_domain="['|', ('name','ilike',self), ('type','ilike',self)]" string="Account Type"/>
                </search>
            </field>
        </record>

        <record id="view_account_type_tree" model="ir.ui.view">
            <field name="name">account.account.type.tree</field>
            <field name="model">account.account.type</field>
            <field name="arch" type="xml">
                <tree string="Account Type">
                    <field name="name"/>
                    <field name="type"/>
                </tree>
            </field>
        </record>

        <record id="view_account_type_form" model="ir.ui.view">
            <field name="name">account.account.type.form</field>
            <field name="model">account.account.type</field>
            <field name="arch" type="xml">
                <form string="Account Type">
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="type"/>
                        </group>
                        <group>
                            <field name="include_initial_balance"/>
                        </group>
                    </group>
                    <separator string="Description"/>
                    <field name="note"/>
                </form>
            </field>
        </record>

        <record id="action_account_type_form" model="ir.actions.act_window">
            <field name="name">Account Types</field>
            <field name="res_model">account.account.type</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="view_account_type_search"/>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Define a new account type
              </p><p>
                An account type is used to determine how an account is used in
                each journal. The deferral method of an account type determines
                the process for the annual closing. Reports such as the Balance
                Sheet and the Profit and Loss report use the category
                (profit/loss or balance sheet).
              </p>
            </field>
        </record>

    </data>
</odoo>
