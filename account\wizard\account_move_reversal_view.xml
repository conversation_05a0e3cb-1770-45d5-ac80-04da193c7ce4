<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="view_account_move_reversal" model="ir.ui.view">
            <field name="name">account.move.reversal.form</field>
            <field name="model">account.move.reversal</field>
            <field name="arch" type="xml">
                <form string="Reverse Journal Entry">
                    <field name="residual" invisible="1"/>
                    <field name="company_id" invisible="1"/>
                    <field name="move_ids" invisible="1"/>
                    <field name="move_type" invisible="1"/>
                    <field name="available_journal_ids" invisible="1"/>
                    <group>
                         <group attrs="{'invisible': ['|',('move_type', 'not in', ('out_invoice', 'in_invoice')),('residual', '=', 0)]}">
                            <field name="refund_method" widget="radio" attrs="{'readonly': [('residual', '=', 0)]}"/>
                         </group>
                         <group attrs="{'invisible': ['|', ('move_type', 'not in', ('out_invoice', 'in_invoice', 'some_invoice')), ('residual', '=', 0)]}">
                            <div attrs="{'invisible':[('refund_method', '!=', 'refund')]}" class="oe_grey" colspan="4">
                               The credit note is created in draft and can be edited before being issued.
                            </div>
                            <div attrs="{'invisible':[('refund_method', '!=', 'cancel')]}" class="oe_grey" colspan="4">
                               The credit note is auto-validated and reconciled with the invoice.
                            </div>
                            <div attrs="{'invisible':[('refund_method', '!=', 'modify')]}" class="oe_grey" colspan="4">
                               The credit note is auto-validated and reconciled with the invoice.
                               The original invoice is duplicated as a new draft.
                            </div>
                         </group>
                    </group>
                    <group>
                        <group>
                            <field name="reason" attrs="{'invisible': [('move_type', '=', 'entry')]}"/>
                            <field name="date_mode" string="Reversal Date" widget="radio"/>
                        </group>
                        <group>
                        <field name="journal_id" domain="[('id', 'in', available_journal_ids)]"/>
                        <field name="date" string="Refund Date" attrs="{'invisible': ['|', ('move_type', 'not in', ('out_invoice', 'in_invoice')), ('date_mode', '!=', 'custom')], 'required':[('date_mode', '=', 'custom')]}"/>
                        <field name="date" attrs="{'invisible': ['|', ('move_type', 'in', ('out_invoice', 'in_invoice')), ('date_mode', '!=', 'custom')], 'required':[('date_mode', '=', 'custom')]}"/>
                        </group>
                    </group>
                    <footer>
                        <button string='Reverse' name="reverse_moves" type="object" class="btn-primary" data-hotkey="q"/>
                        <button string="Cancel" class="btn-secondary" special="cancel" data-hotkey="z"/>
                    </footer>
               </form>
            </field>
        </record>

        <record id="action_view_account_move_reversal" model="ir.actions.act_window">
            <field name="name">Reverse</field>
            <field name="res_model">account.move.reversal</field>
            <field name="view_mode">tree,form</field>
            <field name="view_id" ref="view_account_move_reversal"/>
            <field name="target">new</field>
            <field name="groups_id" eval="[(4, ref('account.group_account_invoice'))]"/>
            <field name="binding_model_id" ref="account.model_account_move" />
            <field name="binding_view_types">list</field>
        </record>
    </data>
</odoo>
