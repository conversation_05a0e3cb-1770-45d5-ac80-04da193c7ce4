# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_check_printing
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> CHEN <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:19+0000\n"
"Last-Translator: <PERSON><PERSON> CHEN <<EMAIL>>, 2022\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_journal.py:0
#, python-format
msgid " : Check Number Sequence"
msgstr " : 支票编号序列"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_margin_left
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_margin_right
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_margin_top
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_margin_left
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_margin_right
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_margin_top
msgid ""
"Adjust the margins of generated checks to make it fit your printer's "
"settings."
msgstr "调整生成的检查页的边距以使其符合打印机的设置"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__check_amount_in_words
msgid "Amount in Words"
msgstr "大写金额"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid "Cancel"
msgstr "取消"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_layout
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_layout
msgid "Check Layout"
msgstr "支票排版"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_margin_left
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_margin_left
msgid "Check Left Margin"
msgstr "支票左边距"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__check_number
msgid "Check Number"
msgstr "支票号码"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_journal_form_inherited
msgid "Check Printing"
msgstr "支票打印"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_margin_right
msgid "Check Right Margin"
msgstr "支票右边距"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_journal__check_sequence_id
msgid "Check Sequence"
msgstr "支票序列"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_margin_top
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_margin_top
msgid "Check Top Margin"
msgstr "支票上边距"

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_payment.py:0
#, python-format
msgid "Check numbers can only consist of digits"
msgstr "支票号码只能由数字组成"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_journal__check_manual_sequencing
#: model:ir.model.fields,help:account_check_printing.field_account_payment__check_manual_sequencing
msgid "Check this option if your pre-printed checks are not numbered."
msgstr "如果您的预先印制支票是没有编号的请勾选此选项。"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.account_journal_dashboard_kanban_view_inherited
msgid "Check to print"
msgstr "待打印支票"

#. module: account_check_printing
#: model:account.payment.method,name:account_check_printing.account_payment_method_check
msgid "Checks"
msgstr "支票"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_journal__check_sequence_id
msgid "Checks numbering sequence."
msgstr "支票编号序列。"

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_journal.py:0
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_payment_check_printing_search
#, python-format
msgid "Checks to Print"
msgstr "待打印支票"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.account_journal_dashboard_kanban_view_inherited
msgid "Checks to print"
msgstr "待打印支票"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_res_company
msgid "Companies"
msgstr "公司"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_res_config_settings
msgid "Config Settings"
msgstr "配置设置"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_res_partner
msgid "Contact"
msgstr "联系人"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__create_uid
msgid "Created by"
msgstr "创建人"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__create_date
msgid "Created on"
msgstr "创建时间"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_payment.py:0
#: code:addons/account_check_printing/models/account_payment.py:0
#, python-format
msgid "Go to the configuration panel"
msgstr "至配置面板"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__id
msgid "ID"
msgstr "ID"

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_payment.py:0
#, python-format
msgid ""
"In order to print multiple checks at once, they must belong to the same bank"
" journal."
msgstr "为了一次打印多张支票，它们必须是相同的银行账户。"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_journal
msgid "Journal"
msgstr "日记账"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_move
msgid "Journal Entry"
msgstr "会计凭证"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks____last_update
msgid "Last Modified on"
msgstr "最后修改时间"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__write_uid
msgid "Last Updated by"
msgstr "最后更新人"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__write_date
msgid "Last Updated on"
msgstr "最后更新时间"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_journal__check_manual_sequencing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__check_manual_sequencing
msgid "Manual Numbering"
msgstr "手动编号"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_payment__payment_method_line_id
msgid ""
"Manual: Pay or Get paid by any method outside of Odoo.\n"
"Payment Acquirers: Each payment acquirer has its own Payment Method. Request a transaction on/to a card thanks to a payment token saved by the partner when buying or subscribing online.\n"
"Check: Pay bills by check and print it from Odoo.\n"
"Batch Deposit: Collect several customer checks at once generating and submitting a batch deposit to your bank. Module account_batch_payment is necessary.\n"
"SEPA Credit Transfer: Pay in the SEPA zone by submitting a SEPA Credit Transfer file to your bank. Module account_sepa is necessary.\n"
"SEPA Direct Debit: Get paid in the SEPA zone thanks to a mandate your partner will have granted to you. Module account_sepa is necessary.\n"
msgstr ""
"手动：通过 Odoo 之外的任何方式支付或获得报酬。\n"
"付款收单行：每个付款收单行都有自己的付款方式。 借助合作夥伴线上购买或订阅时保存的支付令牌，请求对卡进行交易。\n"
"支票：通过支票支付账单并从 Odoo 打印。\n"
"批量存款：一次收集多张客户支票，生成批量存款并将其提交给您的银行。 模块 account_batch_payment 是必要的。\n"
"SEPA Credit Transfer：通过向您的银行提交 SEPA Credit Transfer 單據在 SEPA 区域付款。 模块 account_sepa 是必要的。\n"
"SEPA 直接借记：由于您的合作夥伴授予您的授权，在 SEPA 区域获得付款. 模块 account_sepa 是必要的.\n"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_multi_stub
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_multi_stub
msgid "Multi-Pages Check Stub"
msgstr "多页支票存根"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_journal__check_next_number
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__next_check_number
msgid "Next Check Number"
msgstr "下一个支票号码"

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_journal.py:0
#: code:addons/account_check_printing/wizard/print_prenumbered_checks.py:0
#, python-format
msgid "Next Check Number should only contains numbers."
msgstr "下一个支票号码应该只包含数字。"

#. module: account_check_printing
#: model:ir.model.fields.selection,name:account_check_printing.selection__res_company__account_check_printing_layout__disabled
msgid "None"
msgstr "无"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__payment_method_line_id
#: model:ir.model.fields,field_description:account_check_printing.field_res_partner__property_payment_method_id
#: model:ir.model.fields,field_description:account_check_printing.field_res_users__property_payment_method_id
msgid "Payment Method"
msgstr "付款方式"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_payment_method
msgid "Payment Methods"
msgstr "付款方式"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_payment
msgid "Payments"
msgstr "支付"

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_payment.py:0
#, python-format
msgid ""
"Payments to print as a checks must have 'Check' selected as payment method "
"and not have already been reconciled"
msgstr "以支票方式付款要必须选择'支票'并且未被核对"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid ""
"Please enter the number of the first pre-printed check that you are about to"
" print on."
msgstr "请输入您要打印的第一张预先打印的支票的号码。"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_bank_statement_line__preferred_payment_method_id
#: model:ir.model.fields,field_description:account_check_printing.field_account_move__preferred_payment_method_id
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__preferred_payment_method_id
msgid "Preferred Payment Method"
msgstr "首选付款方式"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_res_partner__property_payment_method_id
#: model:ir.model.fields,help:account_check_printing.field_res_users__property_payment_method_id
msgid ""
"Preferred payment method when paying this vendor. This is used to filter "
"vendor bills by preferred payment method to register payments in mass. Use "
"cases: create bank files for batch wires, check runs."
msgstr "向此供应商付款时的首选付款方式。这用于按首选付款方式筛选供应商帐单，以批量登记付款。用例：为批处理线创建银行文件，检查运行情况。"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid "Print"
msgstr "打印"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_payment_form_inherited
msgid "Print Check"
msgstr "打印支票"

#. module: account_check_printing
#: model:ir.actions.server,name:account_check_printing.action_account_print_checks
msgid "Print Checks"
msgstr "打印支票"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_date_label
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_date_label
msgid "Print Date Label"
msgstr "打印日期标签"

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_payment.py:0
#: model:ir.model,name:account_check_printing.model_print_prenumbered_checks
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
#, python-format
msgid "Print Pre-numbered Checks"
msgstr "打印预印制支票"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_payment_register
msgid "Register Payment"
msgstr "登记付款"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_margin_right
msgid "Right Margin"
msgstr "右边距"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_layout
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_layout
msgid ""
"Select the format corresponding to the check paper you will be printing your checks on.\n"
"In order to disable the printing feature, select 'None'."
msgstr ""
"选择您将要打印的适合支票纸张的格式\n"
"为了禁用打印功能，请选择'没有'"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_payment_form_inherited
msgid "Sent"
msgstr "已发送"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_journal__check_next_number
msgid "Sequence number of the next printed check."
msgstr "下个打印支票的序列编号"

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_payment.py:0
#, python-format
msgid ""
"Something went wrong with Check Layout, please select another layout in "
"Invoicing/Accounting Settings and try again."
msgstr "检查布局出现问题，请在开票/记帐设置中选择其他布局，然后重试。"

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_payment.py:0
#, python-format
msgid ""
"The following numbers are already used:\n"
"%s"
msgstr ""
"已使用以下数字：\n"
"%s"

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_journal.py:0
#, python-format
msgid ""
"The last check number was %s. In order to avoid a check being rejected by "
"the bank, you can only use a greater number."
msgstr "最后一张支票号码是%s。以防支票被银行拒收，您可以用一个稍大一点的号码"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_payment__check_number
msgid ""
"The selected journal is configured to print check numbers. If your pre-"
"printed check paper already has numbers or if the current numbering is "
"wrong, you can change it in the journal configuration page."
msgstr "选择的日记账配置上了打印支票号码。如果预先印制支票已经有号码或者当前的号码是错误的，您可以在日记账配置页面修改"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_multi_stub
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_multi_stub
msgid ""
"This option allows you to print check details (stub) on multiple pages if "
"they don't fit on a single page."
msgstr "如果一页不能打印完，该选项能让您在多页面打印支票明细(存根)"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_date_label
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_date_label
msgid ""
"This option allows you to print the date label on the check as per CPA.\n"
"Disable this if your pre-printed check includes the date label."
msgstr ""
"此选项允许您根据 CPA 在支票上打印日期标签。\n"
"如果预打印的检查包含日期标签，请禁用此选项。"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid ""
"This will allow to save on payments the number of the corresponding check."
msgstr "这能够让你节约相应的付款支票数量"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_payment_form_inherited
msgid "Unmark Sent"
msgstr "无标记送出"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_payment_form_inherited
msgid "Void Check"
msgstr "作废支票"

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_payment.py:0
#, python-format
msgid ""
"You have to choose a check layout. For this, go in Invoicing/Accounting "
"Settings, search for 'Checks layout' and set one."
msgstr "您必须选择检查布局。为此，请进入开票/记帐设置，搜索\"检查布局\"并设置一个。"
