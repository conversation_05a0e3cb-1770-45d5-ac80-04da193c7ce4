<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!--
        Render a res.partner record to be added to the UBL xml document.
        -->
        <template id="export_ubl_invoice_partner">
            <cac:Party
                xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
                xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
                xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2">
                <t t-set="partner" t-value="partner_vals['partner']"/>
                <cbc:WebsiteURI
                    t-if="partner.website"
                    t-esc="partner.website"/>
                <cac:PartyName>
                    <cbc:Name t-esc="partner.name"/>
                </cac:PartyName>
                <cac:Language t-if="partner.lang">
                    <cbc:LocaleCode t-esc="partner.lang"/>
                </cac:Language>
                <cac:PostalAddress>
                    <cbc:StreetName
                        t-if="partner.street"
                        t-esc="partner.street"/>
                    <cbc:AdditionalStreetName
                        t-if="partner.street2"
                        t-esc="partner.street2"/>
                    <cbc:CityName
                        t-if="partner.city"
                        t-esc="partner.city"/>
                    <cbc:PostalZone
                        t-if="partner.zip"
                        t-esc="partner.zip"/>
                    <cbc:CountrySubentity
                        t-if="partner.state_id"
                        t-esc="partner.state_id.name"/>
                    <cbc:CountrySubentityCode
                        t-if="partner.state_id"
                        t-esc="partner.state_id.code"/>
                    <cac:Country
                        t-if="partner.country_id">
                        <cbc:IdentificationCode t-esc="partner.country_id.code"/>
                        <cbc:Name t-esc="partner.country_id.name"/>
                    </cac:Country>
                </cac:PostalAddress>
                <cac:PartyTaxScheme t-if="partner.vat">
                    <cbc:RegistrationName t-esc="partner.name"/>
                    <cbc:CompanyID t-esc="partner.vat"/>
                    <cac:TaxScheme>
                        <cbc:ID
                            schemeID="UN/ECE 5153"
                            schemeAgencyID="6"
                            >VAT</cbc:ID>
                    </cac:TaxScheme>
                </cac:PartyTaxScheme>
                <cac:Contact>
                    <cbc:Name t-esc="partner.name"/>
                    <cbc:Telephone
                        t-if="partner.phone"
                        t-esc="partner.phone"/>
                    <cbc:ElectronicMail
                        t-if="partner.email"
                        t-esc="partner.email"/>
                </cac:Contact>
            </cac:Party>
        </template>

        <!--
        Render an invoice's line to be added to the UBL xml document.
        -->
        <template id="export_ubl_invoice_line">
            <cac:InvoiceLine
                xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
                xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
                xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2">
                <t t-set="line" t-value="line_vals['line']"/>
                <cbc:ID t-esc="line.id"/>
                <cbc:Note t-if="line.discount">Discount (<t t-esc='line.discount'/> %)</cbc:Note>
                <cbc:InvoicedQuantity t-esc="line.quantity"/>
                <cbc:LineExtensionAmount
                    t-att-currencyID="line.currency_id.name"
                    t-esc="format_monetary(line.price_subtotal)"/>
                <cac:TaxTotal
                    t-foreach="tax_details['invoice_line_tax_details'][line]['tax_details'].values()"
                    t-as="tax_detail_vals">
                    <cbc:TaxAmount
                        t-att-currencyID="line.currency_id.name"
                        t-esc="format_monetary(-tax_detail_vals['tax_amount_currency'])"/>
                </cac:TaxTotal>
                <cac:Item>
                    <cbc:Description t-if="line.name" t-esc="line.name.replace('\n', ', ')"/>
                    <cbc:Name t-esc="line.product_id.name"/>
                    <cac:SellersItemIdentification t-if="line.product_id.default_code">
                        <cbc:ID t-esc="line.product_id.default_code"/>
                    </cac:SellersItemIdentification>
                </cac:Item>
                <cac:Price>
                    <cbc:PriceAmount
                        t-att-currencyID="line.currency_id.name"
                        t-esc="format_monetary(line.price_unit)"/>
                </cac:Price>
            </cac:InvoiceLine>
        </template>

        <!--
        Render an invoice to be added to the UBL xml document.
        -->
        <template id="export_ubl_invoice">
            <Invoice
                xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
                xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
                xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2">
                <cbc:CustomizationID t-if="customization_id" t-esc="customization_id"/>
                <cbc:ProfileID t-if="profile_id" t-esc="profile_id"/>
                <cbc:UBLVersionID t-if="ubl_version" t-esc="ubl_version"/>
                <cbc:ID t-esc="record.name"/>
                <cbc:IssueDate t-esc="record.invoice_date"/>
                <cbc:InvoiceTypeCode t-esc="type_code"/>
                <cbc:Note t-if="note" t-esc="note"/>
                <cbc:DocumentCurrencyCode t-esc="record.currency_id.name"/>
                <cbc:BuyerReference t-esc="record.commercial_partner_id.name"/>
                <cac:OrderReference t-if="record.ref">
                    <cbc:ID t-esc="record.ref"/>
                </cac:OrderReference>
                <cac:AccountingSupplierParty t-call="account_edi_ubl.export_ubl_invoice_partner">
                    <t t-set="partner_vals" t-value="supplier_vals"/>
                </cac:AccountingSupplierParty>
                <cac:AccountingCustomerParty t-call="account_edi_ubl.export_ubl_invoice_partner">
                    <t t-set="partner_vals" t-value="customer_vals"/>
                </cac:AccountingCustomerParty>
                <cac:PaymentMeans>
                    <cbc:PaymentMeansCode listID="UN/ECE 4461" t-esc="payment_means_code"/>
                    <cbc:PaymentDueDate t-esc="record.invoice_date_due"/>
                    <cbc:InstructionID t-esc="record.payment_reference"/>
                    <cac:PayeeFinancialAccount t-if="record.journal_id.bank_account_id">
                        <cbc:ID schemeName="IBAN" t-esc="record.journal_id.bank_account_id.acc_number"/>
                        <cac:FinancialInstitutionBranch t-if="record.journal_id.bank_account_id.bank_bic">
                            <cac:FinancialInstitution>
                                <cbc:ID schemeName="BIC" t-esc="record.journal_id.bank_account_id.bank_bic"/>
                            </cac:FinancialInstitution>
                        </cac:FinancialInstitutionBranch>
                    </cac:PayeeFinancialAccount>
                </cac:PaymentMeans>
                <cac:PaymentTerms t-if="record.invoice_payment_term_id">
                    <cbc:Note t-esc="record.invoice_payment_term_id.name"/>
                </cac:PaymentTerms>
                <cac:TaxTotal>
                    <cbc:TaxAmount
                        t-att-currencyID="record.currency_id.name"
                        t-esc="format_monetary(record.amount_tax)"/>
                </cac:TaxTotal>
                <cac:LegalMonetaryTotal>
                    <cbc:LineExtensionAmount
                        t-att-currencyID="record.currency_id.name"
                        t-esc="format_monetary(record.amount_untaxed)"/>
                    <cbc:TaxExclusiveAmount
                        t-att-currencyID="record.currency_id.name"
                        t-esc="format_monetary(record.amount_untaxed)"/>
                    <cbc:TaxInclusiveAmount
                        t-att-currencyID="record.currency_id.name"
                        t-esc="format_monetary(record.amount_total)"/>
                    <cbc:PrepaidAmount
                        t-att-currencyID="record.currency_id.name"
                        t-esc="format_monetary(record.amount_total - record.amount_residual)"/>
                    <cbc:PayableAmount
                        t-att-currencyID="record.currency_id.name"
                        t-esc="format_monetary(record.amount_residual)"/>
                </cac:LegalMonetaryTotal>
                <t t-foreach="invoice_line_vals_list" t-as="line_vals">
                    <t t-call="account_edi_ubl.export_ubl_invoice_line"/>
                </t>
            </Invoice>
        </template>
    </data>
</odoo>
