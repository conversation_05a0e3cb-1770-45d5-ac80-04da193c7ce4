.o_hr_dashboard {
    padding: 20px;
    background-color: #f5f5f5;

    // Style for progress bars
    .progress {
        height: 25px;
        margin-bottom: 10px;

        .progress-bar {
            position: relative;

            .progress-bar-text {
                position: absolute;
                left: 0;
                right: 0;
                text-align: center;
                color: #000;
                font-weight: bold;
                text-shadow: 1px 1px 1px rgba(255, 255, 255, 0.5);
                line-height: 25px;
            }
        }
    }

    .o_hr_dashboard_header {
        margin-bottom: 20px;

        h1 {
            font-size: 24px;
            color: #4c4c4c;

            i {
                margin-right: 10px;
            }
        }
    }

    .o_hr_dashboard_content {
        .o_hr_dashboard_card {
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            overflow: hidden;
            color: white;

            .o_hr_dashboard_card_header {
                padding: 20px;
                display: flex;
                align-items: center;

                i {
                    margin-right: 20px;
                }

                .o_hr_dashboard_card_title {
                    h2 {
                        font-size: 36px;
                        margin: 0;
                        font-weight: bold;
                    }

                    p {
                        margin: 0;
                        font-size: 16px;
                    }
                }
            }

            .o_hr_dashboard_card_footer {
                padding: 10px 20px;
                background-color: rgba(0, 0, 0, 0.1);
                text-align: right;

                a {
                    color: white;

                    &:hover {
                        color: white;
                        text-decoration: none;
                    }
                }
            }
        }

        // Chart containers
        .o_hr_dashboard_chart_container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;

            h3 {
                color: #4c4c4c;
                font-size: 18px;
                margin-bottom: 20px;
                border-bottom: 2px solid #e9ecef;
                padding-bottom: 10px;

                i {
                    margin-right: 10px;
                    color: #007bff;
                }
            }

            .chart-wrapper {
                position: relative;
                height: 400px;
                display: flex;
                justify-content: center;
                align-items: center;
                padding: 10px;

                canvas {
                    max-width: 100%;
                    max-height: 100%;
                }
            }
        }

        // Department statistics container
        .o_hr_dashboard_stats_container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;

            h4 {
                color: #4c4c4c;
                font-size: 16px;
                margin-bottom: 15px;
                border-bottom: 2px solid #e9ecef;
                padding-bottom: 8px;

                i {
                    margin-right: 8px;
                    color: #28a745;
                }
            }

            .department-stats {
                .department-stat-item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 10px 0;
                    border-bottom: 1px solid #f8f9fa;

                    &:last-child {
                        border-bottom: none;
                    }

                    .department-info {
                        display: flex;
                        align-items: center;

                        .department-color {
                            width: 12px;
                            height: 12px;
                            border-radius: 50%;
                            margin-right: 10px;
                            display: inline-block;
                        }

                        .department-name {
                            font-weight: 500;
                            color: #495057;
                        }
                    }

                    .department-numbers {
                        text-align: right;

                        .employee-count {
                            font-weight: bold;
                            color: #007bff;
                        }

                        .percentage {
                            color: #6c757d;
                            font-size: 0.9em;
                            margin-left: 5px;
                        }
                    }
                }
            }
        }

        .o_hr_dashboard_chart_container, .o_hr_dashboard_table_container {
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;

            h3 {
                font-size: 18px;
                color: #4c4c4c;
                margin-bottom: 20px;

                i {
                    margin-right: 10px;
                }
            }

            .o_hr_dashboard_chart {
                height: 300px;
            }
        }
    }

    // Responsive design for mobile devices
    @media (max-width: 768px) {
        .o_hr_dashboard_chart_container {
            .chart-wrapper {
                height: 300px; // Smaller height on mobile for bar chart
                padding: 5px;
            }
        }
    }

    @media (max-width: 576px) {
        padding: 10px;

        .o_hr_dashboard_chart_container {
            padding: 15px;

            h3 {
                font-size: 16px;
            }

            .chart-wrapper {
                height: 250px; // Even smaller on very small screens
                padding: 5px;
            }
        }
    }
}
