# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_check_printing
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 13:17+0000\n"
"PO-Revision-Date: 2018-09-21 13:17+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Serbian (https://www.transifex.com/odoo/teams/41243/sr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_journal.py:58
#, python-format
msgid " : Check Number Sequence"
msgstr ""

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_payment.py:63
#, python-format
msgid "A check memo cannot exceed 60 characters."
msgstr ""

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_margin_left
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_margin_right
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_margin_top
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_margin_left
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_margin_right
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_margin_top
msgid ""
"Adjust the margins of generated checks to make it fit your printer's "
"settings."
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__check_amount_in_words
#: model:ir.model.fields,field_description:account_check_printing.field_account_register_payments__check_amount_in_words
msgid "Amount in Words"
msgstr ""

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid "Cancel"
msgstr "Otkaži"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_layout
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_layout
msgid "Check Layout"
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_margin_left
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_margin_left
msgid "Check Left Margin"
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__check_number
#: model:ir.model.fields,field_description:account_check_printing.field_account_register_payments__check_number
msgid "Check Number"
msgstr ""

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_journal_form_inherited
msgid "Check Printing"
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_journal__check_printing_payment_method_selected
msgid "Check Printing Payment Method Selected"
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_margin_right
msgid "Check Right Margin"
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_journal__check_sequence_id
msgid "Check Sequence"
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_margin_top
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_margin_top
msgid "Check Top Margin"
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_journal__check_manual_sequencing
#: model:ir.model.fields,help:account_check_printing.field_account_payment__check_manual_sequencing
#: model:ir.model.fields,help:account_check_printing.field_account_register_payments__check_manual_sequencing
msgid "Check this option if your pre-printed checks are not numbered."
msgstr ""

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.account_journal_dashboard_kanban_view_inherited
msgid "Check to print"
msgstr ""

#. module: account_check_printing
#: model:account.payment.method,name:account_check_printing.account_payment_method_check
msgid "Checks"
msgstr ""

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_payment_check_printing_search
msgid "Checks To Print"
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_journal__check_sequence_id
msgid "Checks numbering sequence."
msgstr ""

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_journal.py:97
#, python-format
msgid "Checks to Print"
msgstr ""

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.account_journal_dashboard_kanban_view_inherited
msgid "Checks to print"
msgstr ""

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_res_company
msgid "Companies"
msgstr "Kompanije"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__country_code
msgid "Company Country code"
msgstr ""

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__create_uid
msgid "Created by"
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__create_date
msgid "Created on"
msgstr "Kreiran"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__display_name
msgid "Display Name"
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__id
msgid "ID"
msgstr "ID"

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_payment.py:84
#, python-format
msgid ""
"In order to print multiple checks at once, they must belong to the same bank"
" journal."
msgstr ""

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_journal
msgid "Journal"
msgstr "Извештај"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks____last_update
msgid "Last Modified on"
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__write_uid
msgid "Last Updated by"
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__write_date
msgid "Last Updated on"
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_journal__check_manual_sequencing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__check_manual_sequencing
#: model:ir.model.fields,field_description:account_check_printing.field_account_register_payments__check_manual_sequencing
msgid "Manual Numbering"
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_multi_stub
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_multi_stub
msgid "Multi-Pages Check Stub"
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_journal__check_next_number
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__next_check_number
msgid "Next Check Number"
msgstr ""

#. module: account_check_printing
#: selection:res.company,account_check_printing_layout:0
msgid "None"
msgstr "Ništa"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_register_payments__check_number
msgid ""
"Number of the check corresponding to this payment. If your pre-printed check"
" are not already numbered, you can manage the numbering in the journal "
"configuration page."
msgstr ""

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_payment
msgid "Payments"
msgstr "Placanja"

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_payment.py:81
#, python-format
msgid ""
"Payments to print as a checks must have 'Check' selected as payment method "
"and not have already been reconciled"
msgstr ""

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid ""
"Please enter the number of the first pre-printed check that you are about to"
" print on."
msgstr ""

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid "Print"
msgstr "Štampaj"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_payment_form_inherited
msgid "Print Check"
msgstr ""

#. module: account_check_printing
#: model:ir.actions.server,name:account_check_printing.action_account_print_checks
msgid "Print Checks"
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_date_label
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_date_label
msgid "Print Date Label"
msgstr ""

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_payment.py:94
#: model:ir.model,name:account_check_printing.model_print_prenumbered_checks
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
#, python-format
msgid "Print Pre-numbered Checks"
msgstr ""

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_register_payments
msgid "Register Payments"
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_margin_right
msgid "Right Margin"
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_layout
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_layout
msgid ""
"Select the format corresponding to the check paper you will be printing your checks on.\n"
"In order to disable the printing feature, select 'None'."
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_journal__check_next_number
msgid "Sequence number of the next printed check."
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_journal__check_printing_payment_method_selected
msgid ""
"Technical feature used to know whether check printing was enabled as payment"
" method."
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_journal.py:26
#, python-format
msgid ""
"The last check number was %s. In order to avoid a check being rejected by "
"the bank, you can only use a greater number."
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_payment__check_number
msgid ""
"The selected journal is configured to print check numbers. If your pre-"
"printed check paper already has numbers or if the current numbering is "
"wrong, you can change it in the journal configuration page."
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_multi_stub
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_multi_stub
msgid ""
"This option allows you to print check details (stub) on multiple pages if "
"they don't fit on a single page."
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_date_label
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_date_label
msgid ""
"This option allows you to print the date label on the check as per CPA. "
"Disable this if your pre-printed check includes the date label."
msgstr ""

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid ""
"This will allow to save on payments the number of the corresponding check."
msgstr ""

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_payment_form_inherited
msgid "Unmark Sent"
msgstr ""

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_payment.py:116
#, python-format
msgid ""
"You have to choose a check layout. For this, go in Apps, search for 'Checks "
"layout' and install one."
msgstr ""

#. module: account_check_printing
#: selection:res.company,account_check_printing_layout:0
msgid "check in middle"
msgstr ""

#. module: account_check_printing
#: selection:res.company,account_check_printing_layout:0
msgid "check on bottom"
msgstr ""

#. module: account_check_printing
#: selection:res.company,account_check_printing_layout:0
msgid "check on top"
msgstr ""
