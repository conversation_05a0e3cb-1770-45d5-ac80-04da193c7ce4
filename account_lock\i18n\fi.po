# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_lock
# 
# Translators:
# <PERSON><PERSON> <kari.lind<PERSON>@emsystems.fi>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# Konsta Aavaranta, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:19+0000\n"
"Last-Translator: Konsta Aavaranta, 2023\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"Any new All Users Lock Date must be posterior (or equal) to the previous "
"one."
msgstr ""
"Uusi kaikkien käyttäjien lukituspäivä on oltava edellistä myöhäisempi (tai "
"yhtä suuri)."

#. module: account_lock
#: model:ir.model,name:account_lock.model_res_company
msgid "Companies"
msgstr "Yritykset"

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid "The lock date for advisors is irreversible and can't be removed."
msgstr "Neuvonantajien lukituspäivä on peruuttamaton ja sitä ei voi poistaa."

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid "The new tax lock date must be set after the previous lock date."
msgstr ""
"Uusi veroraportin lukituspäivämäärä on asetettava edellisen lukituspäivän "
"jälkeen."

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid "The tax lock date is irreversible and can't be removed."
msgstr ""
"Veroraportin lukituspäivämäärä on peruuttamaton, eikä sitä voi poistaa."

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"You cannot lock a period that has not yet ended. Therefore, the All Users "
"Lock Date must be anterior (or equal) to the last day of the previous month."
msgstr ""
"Et voi lukita ajanjaksoa, joka ei ole vielä päättynyt. Siksi kaikkien "
"käyttäjien lukituspäivämäärän on oltava edellisen kuukauden viimeistä päivää"
" edeltävä (tai yhtä suuri)."

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"You cannot lock a period that has not yet ended. Therefore, the tax lock "
"date must be anterior (or equal) to the last day of the previous month."
msgstr ""
"Et voi lukita ajanjaksoa, joka ei ole vielä päättynyt. Siksi veroraportin "
"lukituspäivämäärä on oltava edellisen kuukauden viimeistä päivää edeltävä "
"päivä (tai yhtä suuri)."

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"You cannot set stricter restrictions on advisors than on users. Therefore, "
"the All Users Lock Date must be anterior (or equal) to the Invoice/Bills "
"Lock Date."
msgstr ""
