# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_lock
# 
# Translators:
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON>cilla (prs) Odoo <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON>d<PERSON><PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:19+0000\n"
"Last-Translator: Ludvi<PERSON> <<EMAIL>>, 2021\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"Any new All Users Lock Date must be posterior (or equal) to the previous "
"one."
msgstr ""
"Toute nouvelle Date de Clôture pour tous les Utilisateurs doit être "
"postérieure (ou égale) à la précédente."

#. module: account_lock
#: model:ir.model,name:account_lock.model_res_company
msgid "Companies"
msgstr "Entreprises"

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid "The lock date for advisors is irreversible and can't be removed."
msgstr ""
"La date de verrouillage pour les conseillers est irréversible et ne peut "
"être supprimée."

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid "The new tax lock date must be set after the previous lock date."
msgstr ""
"La nouvelle date de verrouillage de la taxe doit être configurée après la "
"date de verrouillage précédente."

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid "The tax lock date is irreversible and can't be removed."
msgstr "La date de verrouillage est irréversible et ne peut être supprimée"

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"You cannot lock a period that has not yet ended. Therefore, the All Users "
"Lock Date must be anterior (or equal) to the last day of the previous month."
msgstr ""
"Vous ne pouvez pas clôturer une période qui n'est pas encore réellement "
"terminée. C'est pourquoi la Date de Clôture pour tous les Utilisateurs doit "
"être antérieure (ou égale) au dernier jour du mois précédent."

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"You cannot lock a period that has not yet ended. Therefore, the tax lock "
"date must be anterior (or equal) to the last day of the previous month."
msgstr ""
"Vous ne pouvez pas clôturer une période qui n'est pas terminée. C'est "
"pourquoi la Date de Clôture des Taxes doit être antérieure (ou égale) au "
"dernier jour du mois précédent."

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"You cannot set stricter restrictions on advisors than on users. Therefore, "
"the All Users Lock Date must be anterior (or equal) to the Invoice/Bills "
"Lock Date."
msgstr ""
"Vous ne pouvez pas définir des restrictions plus strictes pour les "
"conseillers que pour les utilisateurs. C'est pourquoi la Date de Clôture "
"pour tous les Utilisateurs doit être antérieure (ou égale) à la Date de "
"Clôture des Factures."
