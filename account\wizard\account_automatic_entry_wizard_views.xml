<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="account_automatic_entry_wizard_form" model="ir.ui.view">
            <field name="name">account.automatic.entry.wizard.form</field>
            <field name="model">account.automatic.entry.wizard</field>
            <field name="arch" type="xml">
                <form>
                    <field name="account_type" invisible="1"/>
                    <field name="company_id" invisible="1"/>
                    <field name="move_line_ids" invisible="1"/>
                    <field name="display_currency_helper" invisible="1"/>
                    <div attrs="{'invisible': [('display_currency_helper', '=', False)]}"  class="alert alert-info text-center" role="status">
                        The selected destination account is set to use a specific currency. Every entry transferred to it will be converted into this currency, causing
                        the loss of any pre-existing foreign currency amount.
                    </div>
                    <field name="action" invisible="context.get('hide_automatic_options')" widget="radio" options="{'horizontal': true}"/>
                    <group>
                        <group attrs="{'invisible': [('action', '!=', 'change_period')]}">
                            <field name="date" string="Recognition Date"/>
                            <field name="expense_accrual_account" string="Accrued Account"
                                   attrs="{'invisible': [('account_type', '!=', 'expense')], 'required': [('account_type', '=', 'expense'), ('action', '=', 'change_period')]}"/>
                            <field name="revenue_accrual_account" string="Accrued Account"
                                   attrs="{'invisible': [('account_type', '!=', 'income')], 'required': [('account_type', '=', 'income'), ('action', '=', 'change_period')]}"/>
                        </group>
                        <group attrs="{'invisible': [('action', '!=', 'change_account')]}">
                            <field name="date" string="Transfer Date"/>
                            <field name="destination_account_id" attrs="{'required': [('action', '=', 'change_account')]}" domain="[('company_id', '=', company_id)]"/>
                        </group>
                        <group>
                            <label for="total_amount" string="Adjusting Amount" attrs="{'invisible': [('action', '!=', 'change_period')]}"/>
                            <div attrs="{'invisible': [('action', '!=', 'change_period')]}">
                                <field name="percentage" style="width:40% !important" class="oe_inline" attrs="{'readonly': [('action', '!=', 'change_period')]}"/>%<span class="px-3"></span>(<field name="total_amount" class="oe_inline"/>)
                            </div>
                            <field name="total_amount" readonly="1" attrs="{'invisible': [('action', '=', 'change_period')]}"/>
                            <field name="journal_id"/>
                        </group>
                    </group>
                    <label for="preview_move_data" string="The following Journal Entries will be generated"/>
                    <field name="preview_move_data" widget="grouped_view_widget"/>
                    <footer>
                        <button string="Create Journal Entries" name="do_action" type="object" class="oe_highlight" data-hotkey="q"/>
                        <button string="Cancel" class="btn btn-secondary" special="cancel" data-hotkey="z"/>
                    </footer>
                </form>
            </field>
        </record>

        <record id="account_automatic_entry_wizard_action" model="ir.actions.act_window">
            <field name="name">Create Automatic Entries for selected Journal Items</field>
            <field name="res_model">account.automatic.entry.wizard</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
        </record>
    </data>
</odoo>
