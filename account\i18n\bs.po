# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-08 06:48+0000\n"
"PO-Revision-Date: 2018-10-08 06:48+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bs\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__state
msgid ""
" * The 'Draft' status is used when a user is encoding a new and unconfirmed Invoice.\n"
" * The 'Open' status is used when user creates invoice, an invoice number is generated. It stays in the open status till the user pays the invoice.\n"
" * The 'In Payment' status is used when payments have been registered for the entirety of the invoice in a journal configured to post entries at bank reconciliation only, and some of them haven't been reconciled with a bank statement line yet.\n"
" * The 'Paid' status is set automatically when the invoice is paid. Its related journal entries may or may not be reconciled.\n"
" * The 'Cancelled' status is used when user cancel invoice."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__code_digits
msgid "# of Digits"
msgstr "Broj znamenki"

#. module: account
#: model:mail.template,report_name:account.mail_template_data_payment_receipt
msgid "${(object.name or '').replace('/','-')}"
msgstr "${(object.name or '').replace('/','-')}"

#. module: account
#: model:mail.template,subject:account.email_template_edi_invoice
msgid "${object.company_id.name} Invoice (Ref ${object.number or 'n/a'})"
msgstr "${object.company_id.name} Faktura (Ref ${object.number or 'n/a'})"

#. module: account
#: model:mail.template,subject:account.mail_template_data_payment_receipt
msgid ""
"${object.company_id.name} Payment Receipt (Ref ${object.name or 'n/a' })"
msgstr "${object.company_id.name} Nalog uplate (Ref ${object.name or 'n/a' })"

#. module: account
#: code:addons/account/models/account.py:937
#, python-format
msgid "%s (Copy)"
msgstr "%s (Kopija)"

#. module: account
#: code:addons/account/models/account.py:242
#: code:addons/account/models/account.py:246
#: code:addons/account/models/account.py:248
#: code:addons/account/models/account.py:597
#: code:addons/account/models/account.py:598
#, python-format
msgid "%s (copy)"
msgstr "%s (kopija)"

#. module: account
#: code:addons/account/models/account.py:688
#, python-format
msgid "%s Sequence"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "&amp;nbsp;<span>on</span>"
msgstr "&amp;nbsp;<span>na</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_invoice_chatter
msgid "&amp;times;"
msgstr "&amp;times;"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_form
msgid "-> Reconcile"
msgstr "-> Zatvori stavke"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
msgid "-> View partially reconciled entries"
msgstr "-> Pregledaj djelomično zatvorene zapise"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_dashboard_onboarding_panel
msgid "/account/static/src/img/account_dashboard_onboarding_bg.jpg"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_onboarding_panel
msgid "/account/static/src/img/account_invoice_onboarding_bg.jpg"
msgstr ""

#. module: account
#: model:account.payment.term,name:account.account_payment_term_15days
msgid "15 Days"
msgstr "15 dana"

#. module: account
#: model:account.payment.term,name:account.account_payment_term_2months
msgid "2 Months"
msgstr ""

#. module: account
#: model:account.payment.term,name:account.account_payment_term_net
msgid "30 Net Days"
msgstr "30 Neto dana"

#. module: account
#: model:account.payment.term,name:account.account_payment_term_advance
msgid "30% Advance End of Following Month"
msgstr "30% avansa do kraja sljedećeg mjeseca"

#. module: account
#: model:account.payment.term,name:account.account_payment_term_45days
msgid "45 Days"
msgstr ""

#. module: account
#: code:addons/account/models/account.py:686
#, python-format
msgid ": Refund"
msgstr ": Knjižno odobrenje"

#. module: account
#: model:mail.template,body_html:account.mail_template_data_payment_receipt
msgid ""
"<?xml version=\"1.0\"?>\n"
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear ${object.partner_id.name}<br/><br/>\n"
"        Thank you for your payment.\n"
"        Here is your payment receipt <strong>${(object.name or '').replace('/','-')}</strong> amounting\n"
"        to <strong>${format_amount(object.amount, object.currency_id)}</strong> from ${object.company_id.name}.\n"
"        <br/><br/>\n"
"        Do not hesitate to contact us if you have any question.\n"
"        <br/><br/>\n"
"        Best regards,<br/>\n"
"        % if user and user.signature:\n"
"        ${user.signature | safe}\n"
"        % endif\n"
"    </p>\n"
"</div>\n"
msgstr ""

#. module: account
#: model:mail.template,body_html:account.email_template_edi_invoice
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear ${object.partner_id.name}\n"
"        % if object.partner_id.parent_id:\n"
"            (${object.partner_id.parent_id.name})\n"
"        % endif\n"
"        <br/><br/>\n"
"        Here is your \n"
"        % if object.number:\n"
"            invoice <strong>${object.number}</strong>\n"
"        % else:\n"
"            invoice\n"
"        %endif\n"
"        % if object.origin:\n"
"            (with reference: ${object.origin})\n"
"        % endif\n"
"        amounting in <strong>${format_amount(object.amount_total, object.currency_id)}</strong>\n"
"        from ${object.company_id.name}.\n"
"        % if object.state=='paid':\n"
"            This invoice is already paid.\n"
"        % else:\n"
"            Please remit payment at your earliest convenience.\n"
"        % endif\n"
"        <br/><br/>\n"
"        Do not hesitate to contact us if you have any question.\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_my_home_menu_invoice
msgid "<em>Draft Invoice</em>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_invoice_page
msgid "<i class=\"fa fa-download\"/> Download"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_invoice_page
msgid "<i class=\"fa fa-fw fa-comments\"/><b>Send message</b>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
msgid ""
"<i class=\"fa fa-fw o_button_icon fa-arrow-right\"/> Configure Email Servers"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_invoice_page
msgid "<i class=\"fa fa-print\"/> Print"
msgstr "<i class=\"fa fa-print\"/> Štampaj"

#. module: account
#: code:addons/account/models/account_invoice.py:699
#, python-format
msgid ""
"<p>You can control the invoice from your vendor based on what you purchased "
"or received.</p>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
msgid ""
"<span attrs=\"{'invisible': [('composition_mode', '!=', 'mass_mail')]}\">\n"
"                                        <strong>Email mass mailing</strong> on\n"
"                                        <span>the selected records</span>\n"
"                                    </span>\n"
"                                    <span>Followers of the document and</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_my_invoices
msgid ""
"<span class=\"badge badge-pill badge-info\"><i class=\"fa fa-fw fa-clock-o\""
" aria-label=\"Opened\" title=\"Opened\" role=\"img\"/><span class=\"d-none d"
"-md-inline\"> Waiting for Payment</span></span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_my_invoices
msgid ""
"<span class=\"badge badge-pill badge-success\"><i class=\"fa fa-fw fa-"
"check\" aria-label=\"Paid\" title=\"Paid\" role=\"img\"/><span "
"class=\"d-none d-md-inline\"> Paid</span></span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_my_invoices
msgid ""
"<span class=\"badge badge-pill badge-warning\"><i class=\"fa fa-fw fa-"
"remove\" aria-label=\"Cancelled\" title=\"Cancelled\" role=\"img\"/><span "
"class=\"d-none d-md-inline\"> Cancelled</span></span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_form
msgid ""
"<span class=\"o_form_label\" attrs=\"{'invisible': ['|',('state','!=','draft'), ('type','!=','in_invoice')]}\">Draft Bill</span>\n"
"                        <span class=\"o_form_label\" attrs=\"{'invisible': [('sequence_number_next_prefix','=',False)]}\">- First Number:</span>\n"
"                        <span class=\"o_form_label\" attrs=\"{'invisible': ['|',('state','!=','draft'), ('type','!=','in_refund')]}\">Draft Credit Note</span>\n"
"                        <span class=\"o_form_label\" attrs=\"{'invisible': ['|',('state', '=', 'draft'), ('type','!=','in_invoice')]}\">Bill </span>\n"
"                        <span class=\"o_form_label\" attrs=\"{'invisible': ['|',('state', '=', 'draft'), ('type','!=','in_refund')]}\">Credit Note </span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_form
msgid ""
"<span class=\"o_form_label\" attrs=\"{'invisible': "
"['|',('state','=','draft'), ('type','!=','out_refund')]}\">Credit "
"Note</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid ""
"<span class=\"o_form_label\" attrs=\"{'invisible': [('match_amount', '!=', "
"'between')]}\">and</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.validate_account_move_view
msgid ""
"<span class=\"o_form_label\">All selected journal entries will be validated "
"and posted. You won't be able to modify them afterwards.</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Default Taxes</span>\n"
"                                    <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Fiscal Localization</span>\n"
"                                        <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Main Currency</span>\n"
"                                    <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Rounding Method</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_line_form
msgid "<span class=\"o_form_label\">of the month</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.partner_view_buttons
msgid "<span class=\"o_stat_text\">Invoiced</span>"
msgstr "<span class=\"o_stat_text\">Fakturisano</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.setup_bank_account_wizard
msgid ""
"<span class=\"text-muted\">Only journals not yet linked to a bank account "
"are proposed</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid ""
"<span groups=\"account.group_show_line_subtotals_tax_excluded\">Amount</span>\n"
"                                    <span groups=\"account.group_show_line_subtotals_tax_included\">Total Price</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span name=\"button_import_placeholder\"/> Statements"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span role=\"separator\">New</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span role=\"separator\">Reconciliation</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span role=\"separator\">View</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span title=\"Balance in Odoo\">Balance in General Ledger</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span title=\"Latest Statement\">Latest Statement</span>"
msgstr "<span title=\"Latest Statement\">Zadnji nalog knjiženja</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_position_form
msgid "<span> From </span>"
msgstr "<span> Iz </span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_position_form
msgid "<span> To </span>"
msgstr "<span> Na </span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "<span>Amount Paid</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "<span>Balance</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<span>Description</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<span>Disc.(%)</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>Import Bills</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "<span>Invoice Date</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "<span>Invoice Number</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>Journal Entries</span>"
msgstr "<span>Dnevnički zapisi</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>New Bill</span>"
msgstr "<span>Novi račun</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>New Invoice</span>"
msgstr "<span>Nova faktura</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>New</span>"
msgstr "<span>Novi</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>Operations</span>"
msgstr "<span>Operacije</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "<span>Original Amount</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<span>Quantity</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>Reporting</span>"
msgstr "<span>Izvještavanje</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<span>Source Document</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<span>Taxes</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<span>Unit Price</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>View</span>"
msgstr "<span>Pogledaj</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong class=\"mr16\">Subtotal</strong>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid ""
"<strong class=\"text-center\">Scan me with your banking "
"app.</strong><br/><br/>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid ""
"<strong class=\"text-center\">The SEPA QR Code informations are not set "
"correctly.</strong><br/>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_invoice_page
msgid "<strong class=\"text-muted\">Your Contact</strong>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document_with_payments
msgid "<strong>Amount Due</strong>"
msgstr "<strong>Iznos van valute</strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "<strong>Company:</strong>"
msgstr "<strong>Kompanija:</strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Customer Code:</strong>"
msgstr "<strong>Kod kupca:</strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "<strong>Customer: </strong>"
msgstr "<strong>Kupac: </strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Description:</strong>"
msgstr "<strong>Opis:</strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Due Date:</strong>"
msgstr "<strong>Datum valute plaćanja:</strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "<strong>Entries Sorted By:</strong>"
msgstr "<strong>Zapisi sortirani po:</strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.setup_financial_year_opening_form
msgid "<strong>Fiscal Year End</strong>"
msgstr "<strong>Kraj fiskalne godine</strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Invoice Date:</strong>"
msgstr "<strong>Datum fakturisanja:</strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "<strong>Journal:</strong>"
msgstr "<strong>Dnevnik:</strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "<strong>Memo: </strong>"
msgstr "<strong>Dopis: </strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "<strong>Payment Amount: </strong>"
msgstr "<strong>Iznos plaćanja: </strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "<strong>Payment Date: </strong>"
msgstr "<strong>Datum plaćanja: </strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "<strong>Payment Method: </strong>"
msgstr "<strong>Metoda plaćanja: </strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Reference:</strong>"
msgstr "<strong>Referanca:</strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Source:</strong>"
msgstr "<strong>Izvor:</strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Subtotal</strong>"
msgstr "<strong>Podukupno</strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "<strong>Target Moves:</strong>"
msgstr "<strong>Ciljana knjiženja:</strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "<strong>Total</strong>"
msgstr "<strong>Ukupno</strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_account_kanban
msgid "<strong>Type: </strong>"
msgstr "<strong>Tip: </strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "<strong>Vendor: </strong>"
msgstr "<strong>Dobavljač: </strong>"

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_view_bank_statement_tree
msgid ""
"A Cash Register allows you to manage cash entries in your cash\n"
"                journals. This feature provides an easy way to follow up cash\n"
"                payments on a daily basis. You can enter the coins that are in\n"
"                your cash box, and then post entries when money comes in or\n"
"                goes out of the cash box."
msgstr ""

#. module: account
#: code:addons/account/models/account_invoice.py:1895
#, python-format
msgid "A Payment Term should have only one line of type Balance."
msgstr "Uslovi plaćanja bi trabali imati samo jednu stavku tipa Saldo."

#. module: account
#: code:addons/account/models/account.py:854
#, python-format
msgid "A bank account can belong to only one journal."
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_bank_statement_tree
msgid ""
"A bank statement is a summary of all financial transactions\n"
"                occurring over a given period of time on a bank account. You\n"
"                should receive this periodicaly from your bank."
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_bank_statement_line
msgid "A bank statement line is a financial transaction on a bank account"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_move_journal_line
msgid ""
"A journal entry consists of several journal items, each of\n"
"                which is either a debit or a credit transaction."
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_journal_form
msgid ""
"A journal is used to record transactions of all accounting data\n"
"                related to the day-to-day business."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:324
#, python-format
msgid ""
"A payment journal entry generated in a journal configured to post entries "
"only when payments are reconciled with a bank statement cannot be manually "
"posted. Those will be posted automatically after performing the bank "
"reconciliation."
msgstr ""

#. module: account
#: code:addons/account/models/reconciliation_widget.py:681
#, python-format
msgid "A reconciliation must involve at least 2 move lines."
msgstr "Zatvaranje mora da uključi minimalno 2 stavke knjiženja."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"A rounding per line is advised if your prices are tax-included. That way, "
"the sum of line subtotals equals the total with taxes."
msgstr ""

#. module: account
#: code:addons/account/models/account_bank_statement.py:527
#: code:addons/account/models/account_bank_statement.py:530
#, python-format
msgid "A selected move line was already reconciled."
msgstr "Odabrana stavka knjiženja je već zatvorena."

#. module: account
#: code:addons/account/models/account_bank_statement.py:538
#, python-format
msgid "A selected statement line was already reconciled with an account move."
msgstr ""

#. module: account
#: sql_constraint:account.fiscal.position.tax:0
msgid "A tax fiscal position could be defined only once time on same taxes."
msgstr ""
"Fiskalna pozicija poreza se može definisati samo jednom za jedan porez."

#. module: account
#: model:res.groups,name:account.group_warning_account
msgid "A warning can be set on a partner (Account)"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__access_warning
msgid "Access warning"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:559
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:561
#: code:addons/account/static/src/xml/account_reconciliation.xml:181
#: code:addons/account/static/src/xml/account_reconciliation.xml:282
#: code:addons/account/static/src/xml/account_reconciliation.xml:307
#: model:ir.model,name:account.model_account_account
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__account_id
#: model:ir.model.fields,field_description:account.field_account_invoice__account_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line__account_id
#: model:ir.model.fields,field_description:account.field_account_move__dummy_account_id
#: model:ir.model.fields,field_description:account.field_account_move_line__account_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__account_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__account_id
#: model_terms:ir.ui.view,arch_db:account.report_journal
#: model_terms:ir.ui.view,arch_db:account.view_account_form
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
#: model_terms:ir.ui.view,arch_db:account.view_account_search
#, python-format
msgid "Account"
msgstr "Konto"

#. module: account
#: code:addons/account/models/account_move.py:913
#, python-format
msgid ""
"Account %s (%s) does not allow reconciliation. First change the "
"configuration of this account to allow it."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_accountant
msgid "Account Accountant"
msgstr "Računovođa"

#. module: account
#: model:ir.model,name:account.model_account_cash_rounding
msgid "Account Cash Rounding"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_common_report
msgid "Account Common Report"
msgstr "Računovodstveni izvještaji"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__currency_id
#: model:ir.model.fields,field_description:account.field_account_account_template__currency_id
msgid "Account Currency"
msgstr "Valuta"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template__account_dest_id
msgid "Account Destination"
msgstr "Ciljni konto"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Account Entry"
msgstr "Stavka knjiženja"

#. module: account
#: model:ir.model,name:account.model_account_group
#: model_terms:ir.ui.view,arch_db:account.view_account_group_form
#: model_terms:ir.ui.view,arch_db:account.view_account_group_tree
msgid "Account Group"
msgstr "Računovodstvena grupa"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_group_tree
msgid "Account Groups"
msgstr "Grupe konta"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__company_partner_id
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__partner_id
msgid "Account Holder"
msgstr "Nosioc računa"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__acc_holder_name
msgid "Account Holder Name"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_invoice_send
msgid "Account Invoice Send"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__journal_id
#: model:ir.model.fields,field_description:account.field_res_partner_bank__journal_id
#: model_terms:ir.ui.view,arch_db:account.view_account_bank_journal_tree
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_tree
msgid "Account Journal"
msgstr "Nalog za knjiženje računa"

#. module: account
#: model:ir.model,name:account.model_report_account_report_journal
msgid "Account Journal Report"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__account_ids
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__account_ids
#: model_terms:ir.ui.view,arch_db:account.view_account_position_form
msgid "Account Mapping"
msgstr "Mapiranje konta"

#. module: account
#: model:ir.model,name:account.model_account_move_reversal
#: model_terms:ir.ui.view,arch_db:account.view_account_move_reversal
msgid "Account Move Reversal"
msgstr "Obrnuto knjiženje"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__bank_acc_number
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__acc_number
msgid "Account Number"
msgstr "Broj računa"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__property_account_payable_id
msgid "Account Payable"
msgstr "Dugovni konto"

#. module: account
#: model:ir.model,name:account.model_account_print_journal
msgid "Account Print Journal"
msgstr "Ispis dnevnika"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_category_property_form
msgid "Account Properties"
msgstr "Svojstva konta"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__property_account_receivable_id
msgid "Account Receivable"
msgstr "Potražni konto"

#. module: account
#: model:ir.model,name:account.model_account_reconciliation_widget
msgid "Account Reconciliation widget"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template__account_src_id
msgid "Account Source"
msgstr "Izvor Računa"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_bank_statement_graph
#: model_terms:ir.ui.view,arch_db:account.account_bank_statement_pivot
#: model_terms:ir.ui.view,arch_db:account.account_move_line_graph_date
#: model_terms:ir.ui.view,arch_db:account.account_move_line_graph_date_cash_basis
msgid "Account Statistics"
msgstr "Statistike konta"

#. module: account
#: model:ir.model,name:account.model_account_account_tag
msgid "Account Tag"
msgstr "Oznaka konta"

#. module: account
#: model:ir.actions.act_window,name:account.account_tag_action
msgid "Account Tags"
msgstr "Računovodstvene oznake"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_tax_view_tree
#: model_terms:ir.ui.view,arch_db:account.view_tax_form
#: model_terms:ir.ui.view,arch_db:account.view_tax_tree
msgid "Account Tax"
msgstr "Konto poreza"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_template_form
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_template_tree
msgid "Account Tax Template"
msgstr "Predložak poreznog računa"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_taxcloud
msgid "Account TaxCloud"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
#: model_terms:ir.ui.view,arch_db:account.view_account_template_form
#: model_terms:ir.ui.view,arch_db:account.view_account_template_search
#: model_terms:ir.ui.view,arch_db:account.view_account_template_tree
msgid "Account Template"
msgstr "Predložak računa"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__property_stock_valuation_account_id
#: model:ir.model.fields,field_description:account.field_res_company__property_stock_valuation_account_id
msgid "Account Template for Stock Valuation"
msgstr "Prijedlog knjiženja za obračun zaliha"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_template_form
msgid "Account Templates"
msgstr "Predlošci računa"

#. module: account
#: model:ir.model,name:account.model_account_account_type
#: model:ir.model.fields,field_description:account.field_account_account_type__name
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__related_acc_type
#: model_terms:ir.ui.view,arch_db:account.view_account_search
#: model_terms:ir.ui.view,arch_db:account.view_account_template_search
#: model_terms:ir.ui.view,arch_db:account.view_account_type_form
#: model_terms:ir.ui.view,arch_db:account.view_account_type_search
#: model_terms:ir.ui.view,arch_db:account.view_account_type_tree
msgid "Account Type"
msgstr "Tip konta"

#. module: account
#: model:ir.model.fields,help:account.field_account_account__user_type_id
#: model:ir.model.fields,help:account.field_account_move_line__user_type_id
msgid ""
"Account Type is used for information purpose, to generate country-specific "
"legal reports, and set the rules to close a fiscal year and generate opening"
" entries."
msgstr ""
"Tip konta se koristi u informacijske svrhe, da bi se generisali državno "
"specifični pravni izvještaji, i postavila pravila za zatvaranje fiskalne "
"godine i otvaranje nove."

#. module: account
#: model:ir.actions.act_window,name:account.action_account_type_form
msgid "Account Types"
msgstr "Tipovi konta"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__type_control_ids
msgid "Account Types Allowed"
msgstr "Dozvoljeni tipovi konta"

#. module: account
#: model:ir.model,name:account.model_account_unreconcile
msgid "Account Unreconcile"
msgstr "Poništavajne zatvaranja konta"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_group_search
msgid "Account group"
msgstr "Računovodstvena grupa"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_group_search
msgid "Account groups"
msgstr "Računovodstvene grupe"

#. module: account
#: model:ir.model.fields,help:account.field_account_setup_bank_manual_config__acc_holder_name
msgid ""
"Account holder name, in case it is different than the name of the Account "
"Holder"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account__account_src_id
msgid "Account on Product"
msgstr "Konto na proizvodu"

#. module: account
#: model:ir.model,name:account.model_report_account_report_invoice_with_payments
msgid "Account report with payment lines"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_template__tag_ids
#: model:ir.model.fields,field_description:account.field_account_tax_template__tag_ids
msgid "Account tag"
msgstr "Oznaka konta"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__refund_account_id
msgid ""
"Account that will be set on invoice tax lines for credit notes. Leave empty "
"to use the expense account."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__account_id
#: model:ir.model.fields,help:account.field_account_tax_template__account_id
msgid ""
"Account that will be set on invoice tax lines for invoices. Leave empty to "
"use the expense account."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_template__refund_account_id
msgid ""
"Account that will be set on invoice tax lines for refunds. Leave empty to "
"use the expense account."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__cash_basis_base_account_id
#: model:ir.model.fields,help:account.field_account_tax_template__cash_basis_base_account_id
msgid ""
"Account that will be set on lines created in cash basis journal entry and "
"used to keep track of the tax base amount."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account__account_dest_id
msgid "Account to Use Instead"
msgstr "Konto za koristiti umjesto"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__cash_basis_account_id
#: model:ir.model.fields,help:account.field_account_tax_template__cash_basis_account_id
msgid ""
"Account used as counterpart for the journal entry, for taxes eligible based "
"on payments."
msgstr ""

#. module: account
#: model:ir.actions.server,name:account.ir_cron_reverse_entry_ir_actions_server
#: model:ir.cron,cron_name:account.ir_cron_reverse_entry
#: model:ir.cron,name:account.ir_cron_reverse_entry
msgid "Account; Reverse entries"
msgstr ""

#. module: account
#: model:ir.ui.menu,name:account.account_account_menu
#: model:ir.ui.menu,name:account.menu_finance_entries
#: model_terms:ir.ui.view,arch_db:account.product_template_form_view
#: model_terms:ir.ui.view,arch_db:account.view_account_analytic_line_form_inherit_account
msgid "Accounting"
msgstr "Računovodstvo"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Accounting App Options"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__accounting_date
#: model:ir.model.fields,field_description:account.field_account_invoice__date
#: model:ir.model.fields,field_description:account.field_account_invoice_refund__date
msgid "Accounting Date"
msgstr "Datum knjiženja"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
msgid "Accounting Documents"
msgstr "Dokumenti računovodstva"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_entries_accounting_entries
#: model_terms:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Accounting Entries"
msgstr "Knjiženja"

#. module: account
#: model:ir.actions.act_window,name:account.open_account_journal_dashboard_kanban
msgid "Accounting Overview"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Accounting-related settings are managed on"
msgstr "Postavke računovodstva se vode na"

#. module: account
#: selection:account.account.tag,applicability:0
#: model_terms:ir.ui.view,arch_db:account.tax_adjustments_wizard
#: model_terms:ir.ui.view,arch_db:account.view_account_search
msgid "Accounts"
msgstr "Konta"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__account_control_ids
msgid "Accounts Allowed"
msgstr "Dozvoljena konta"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_position_template_form
msgid "Accounts Mapping"
msgstr "Mapiranje konta"

#. module: account
#: model:ir.model,name:account.model_account_fiscal_position_account_template
msgid "Accounts Mapping Template of Fiscal Position"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_fiscal_position_account
msgid "Accounts Mapping of Fiscal Position"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_needaction
#: model:ir.model.fields,field_description:account.field_account_invoice__message_needaction
#: model:ir.model.fields,field_description:account.field_account_payment__message_needaction
msgid "Action Needed"
msgstr "Potrebna akcija"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_entries_actions
msgid "Actions"
msgstr "Akcije"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Activate Other Currencies"
msgstr "Aktivirajte ostale valute"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag__active
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__active
#: model:ir.model.fields,field_description:account.field_account_incoterms__active
#: model:ir.model.fields,field_description:account.field_account_journal__active
#: model:ir.model.fields,field_description:account.field_account_payment_term__active
#: model:ir.model.fields,field_description:account.field_account_tax__active
#: model:ir.model.fields,field_description:account.field_account_tax_template__active
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Active"
msgstr "Aktivan"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__active_domain
msgid "Active domain"
msgstr "Aktivni domen"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__activity_ids
msgid "Activities"
msgstr "Aktivnosti"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__activity_state
msgid "Activity State"
msgstr "Status aktivnosti"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:17
#, python-format
msgid "Add"
msgstr "Dodaj"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_form
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_refund
msgid "Add Credit Note"
msgstr "Dodaj knjižno odobrenje"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__add_sign
msgid "Add Sign"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_bank_account_step
msgid "Add a bank"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_journal_form
msgid "Add a journal"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_form
msgid "Add a line"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_form
msgid "Add a new account"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.account_tag_action
msgid "Add a new tag"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_form
msgid "Add a note"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Add a payment QR code to your invoices"
msgstr ""

#. module: account
#: selection:account.cash.rounding,strategy:0
msgid "Add a rounding line"
msgstr "Dodaj stavku zaokruživanja"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__has_second_line
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__has_second_line
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Add a second line"
msgstr "Dodaj drugu stavku"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_form
msgid "Add a section"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Add an EPC QR code to your invoices so that your customers can pay instantly"
" with their mobile banking application. EPC QR codes are used by many "
"European banks to process SEPA payments."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
msgid "Add an internal note..."
msgstr "Dodaj internu zabilješku ..."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
msgid "Add contacts to notify..."
msgstr "Dodaj kontakte kojima ide obavijest..."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__partner_ids
msgid "Additional Contacts"
msgstr "Dodatni kontakti"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__comment
msgid "Additional Information"
msgstr "Dodatne informacije"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Additional notes..."
msgstr "Dodatne zabilješke..."

#. module: account
#: selection:account.tax,type_tax_use:0
#: selection:account.tax.template,type_tax_use:0
msgid "Adjustment"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__tax_id
msgid "Adjustment Tax"
msgstr "Prilagođavanje poreza"

#. module: account
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__adjustment_type
msgid "Adjustment Type"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_template_form
#: model_terms:ir.ui.view,arch_db:account.view_tax_form
msgid "Advanced Options"
msgstr "Napredne opcije"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Advanced Settings"
msgstr "Napredna podešavanja"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__include_base_amount
msgid "Affect Base of Subsequent Taxes"
msgstr "Utiče na osnovu podporeza"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_template__include_base_amount
msgid "Affect Subsequent Taxes"
msgstr "Utiče na podporeze"

#. module: account
#: model:ir.actions.report,name:account.action_report_aged_partner_balance
msgid "Aged Partner Balance"
msgstr "Zreli saldo partnera"

#. module: account
#: model:ir.model,name:account.model_report_account_report_agedpartnerbalance
msgid "Aged Partner Balance Report"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__alias_id
msgid "Alias"
msgstr "Alias"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__alias_name
msgid "Alias Name for Vendor Bills"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__alias_domain
msgid "Alias domain"
msgstr "Alias domena"

#. module: account
#: selection:account.common.journal.report,target_move:0
#: selection:account.common.report,target_move:0
#: selection:account.print.journal,target_move:0
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "All Entries"
msgstr "Sve stavke"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__all_lines_reconciled
msgid "All Lines Reconciled"
msgstr "Sve su stavke zatvorene"

#. module: account
#: selection:account.common.journal.report,target_move:0
#: selection:account.common.report,target_move:0
#: selection:account.print.journal,target_move:0
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "All Posted Entries"
msgstr "Sve proknjižene stavke"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:49
#, python-format
msgid ""
"All invoices and payments have been matched, your accounts' balances are "
"clean."
msgstr "Sve fakture i plaćanja su bila usklađena, salda vaših konta su čista."

#. module: account
#: model:ir.model.fields,help:account.field_account_move__state
msgid ""
"All manually created new journal entries are usually in the status "
"'Unposted', but you can set the option to skip that status on the related "
"journal. In that case, they will behave as journal entries automatically "
"created by the system on document validation (invoices, bank statements...) "
"and will be created in 'Posted' status."
msgstr ""
"Ručno kreirani dnevnički zapisi su obično u statusu 'neknjižen', ali možete "
"postaviti opciju da preskače taj status na povezanom dnevniku. U tom "
"slučaju, ponašati će se kao dnevnički zapisi koje sistem kreira automatski  "
"na potvrdi dokumenata (fakture, izvodi ...) i biti će kreirane u statusu "
"'knjiženo'."

#. module: account
#: code:addons/account/models/account_bank_statement.py:243
#, python-format
msgid ""
"All the account entries lines must be processed in order to close the "
"statement."
msgstr ""
"Svi knjigovodsveni zapisi moraju biti obrađeni da bi ste zatvorili nalog."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__update_posted
msgid "Allow Cancelling Entries"
msgstr "Dozvoli poništavanje stavki"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_template__reconcile
msgid "Allow Invoices & payments Matching"
msgstr "Sva plaćanja i fakture su usklađena"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_product_margin
msgid "Allow Product Margin"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__reconcile
msgid "Allow Reconciliation"
msgstr "Dozvoli usklađivanje"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_check_printing
msgid "Allow check printing and deposits"
msgstr "Dozvoli štampanje čekova i depozite"

#. module: account
#: model:res.groups,name:account.group_cash_rounding
msgid "Allow the cash rounding management"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Allow to configure taxes using cash basis"
msgstr ""

#. module: account
#: model:res.groups,name:account.group_fiscal_year
msgid "Allow to define fiscal years of more or less than a year"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Allows to tag analytic entries and to manage analytic distributions"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Allows you to use the analytic accounting."
msgstr "Dozvoljava Vam korištenje analitičkog računovodstva."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:208
#: code:addons/account/static/src/xml/account_reconciliation.xml:289
#: code:addons/account/static/src/xml/account_reconciliation.xml:306
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__amount
#: model:ir.model.fields,field_description:account.field_account_move__amount
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__amount
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_amount
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_amount
#: model:ir.model.fields,field_description:account.field_account_tax__amount
#: model:ir.model.fields,field_description:account.field_account_tax_template__amount
#: model:ir.model.fields,field_description:account.field_cash_box_in__amount
#: model:ir.model.fields,field_description:account.field_cash_box_out__amount
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__amount
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_form
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_tree
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
#: model_terms:ir.ui.view,arch_db:account.view_account_supplier_payment_tree
#: model_terms:ir.ui.view,arch_db:account.view_invoice_tax_form
#: model_terms:ir.ui.view,arch_db:account.view_invoice_tax_tree
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
#, python-format
msgid "Amount"
msgstr "Iznos"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line__price_total
msgid "Amount (with Taxes)"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line__price_subtotal
msgid "Amount (without Taxes)"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__amount_currency
#: model:ir.model.fields,field_description:account.field_account_move_line__amount_currency
msgid "Amount Currency"
msgstr "Valuta iznosa"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_tax__amount_rounding
msgid "Amount Delta"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__residual
#: model_terms:ir.ui.view,arch_db:account.invoice_tree
#: model_terms:ir.ui.view,arch_db:account.portal_my_invoices
msgid "Amount Due"
msgstr "Iznos van valute"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__residual_company_signed
msgid "Amount Due in Company Currency"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__residual_signed
msgid "Amount Due in Invoice Currency"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_total_amount
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_total_amount
msgid "Amount Matching"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_total_amount_param
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_total_amount_param
msgid "Amount Matching %"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_amount_max
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_amount_max
msgid "Amount Max Parameter"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_amount_min
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_amount_min
msgid "Amount Min Parameter"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_nature
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_nature
msgid "Amount Nature"
msgstr ""

#. module: account
#: selection:account.reconcile.model,match_nature:0
#: selection:account.reconcile.model.template,match_nature:0
msgid "Amount Paid"
msgstr "Plaćeni iznos"

#. module: account
#: selection:account.reconcile.model,match_nature:0
#: selection:account.reconcile.model.template,match_nature:0
msgid "Amount Paid/Received"
msgstr ""

#. module: account
#: selection:account.reconcile.model,match_nature:0
#: selection:account.reconcile.model.template,match_nature:0
msgid "Amount Received"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line__price_subtotal_signed
msgid "Amount Signed"
msgstr "Ovjeren iznos"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_tax__amount_total
msgid "Amount Total"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__amount_type
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__amount_type
msgid "Amount Type"
msgstr "Vrsta iznosa"

#. module: account
#: model:ir.model.fields,help:account.field_account_partial_reconcile__amount
msgid "Amount concerned by this matching. Assumed to be always positive"
msgstr ""
"Iznos koji se tiče ovog usklađivanja. Pretpostavlja se da je uvijek "
"pozitivan."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__amount_currency
msgid "Amount in Currency"
msgstr "Iznos u valuti"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Amount type"
msgstr "Tip iznosa"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:68
#, python-format
msgid "Amount:"
msgstr "Iznos:"

#. module: account
#: sql_constraint:account.fiscal.position.account:0
msgid ""
"An account fiscal position could be defined only once time on same accounts."
msgstr ""
"Fiskalna pozicija konta može biti definisana samo jednom za neka konta."

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_form
msgid ""
"An account is part of a ledger allowing your company\n"
"                to register all kinds of debit and credit transactions.\n"
"                Companies present their annual accounts in two main parts: the\n"
"                balance sheet and the income statement (profit and loss\n"
"                account). The annual accounts of a company are required by law\n"
"                to disclose a certain amount of information."
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_type_form
msgid ""
"An account type is used to determine how an account is used in\n"
"                each journal. The deferral method of an account type determines\n"
"                the process for the annual closing. Reports such as the Balance\n"
"                Sheet and the Profit and Loss report use the category\n"
"                (profit/loss or balance sheet)."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
msgid "Analytic"
msgstr "Analitika"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:189
#, python-format
msgid "Analytic Acc."
msgstr "Analitički konto"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line__account_analytic_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report__account_analytic_id
#: model:ir.model.fields,field_description:account.field_account_move_line__analytic_account_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__analytic_account_id
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Analytic Account"
msgstr "Analitički konto"

#. module: account
#: model:ir.ui.menu,name:account.account_analytic_group_menu
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Analytic Account Groups"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__group_analytic_accounting
#: model:ir.ui.menu,name:account.menu_analytic_accounting
msgid "Analytic Accounting"
msgstr "Analitičko računovodstvo"

#. module: account
#: model:ir.actions.act_window,name:account.action_open_partner_analytic_accounts
#: model:ir.ui.menu,name:account.account_analytic_def_account
#: model_terms:ir.ui.view,arch_db:account.partner_view_button_contracts_count
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Analytic Accounts"
msgstr "Analitički računi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_template__analytic
msgid "Analytic Cost"
msgstr "Analitički trošak"

#. module: account
#: model:ir.ui.menu,name:account.menu_action_analytic_lines_tree
msgid "Analytic Items"
msgstr "Analitičke stavke"

#. module: account
#: model:ir.model,name:account.model_account_analytic_line
msgid "Analytic Line"
msgstr "Analitička stavka"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
msgid "Analytic Lines"
msgstr "Retci analitike"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line__analytic_tag_ids
#: model:ir.model.fields,field_description:account.field_account_invoice_tax__analytic_tag_ids
#: model:ir.model.fields,field_description:account.field_account_move_line__analytic_tag_ids
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__analytic_tag_ids
#: model:ir.model.fields,field_description:account.field_res_config_settings__group_analytic_tags
#: model:ir.ui.menu,name:account.account_analytic_tag_menu
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Analytic Tags"
msgstr "Analitičke oznake"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:193
#, python-format
msgid "Analytic Tags."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_tax__account_analytic_id
msgid "Analytic account"
msgstr "Analitički račun"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__analytic_line_ids
msgid "Analytic lines"
msgstr "Retci analitike"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Analytics"
msgstr "Analitika"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__no_auto_thread
msgid ""
"Answers do not go in the original document discussion thread. This has an "
"impact on the generated message-id."
msgstr ""
"Odgovori ne idu u nit razgovora originalnog dokumenta. Ovo ima uticaja na "
"generisani id poruke."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__tax_exigible
msgid "Appears in VAT report"
msgstr "Pojavljuje se u izvještaju PDV-a"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag__applicability
msgid "Applicability"
msgstr "Primjenjivost"

#. module: account
#: selection:tax.adjustments.wizard,adjustment_type:0
msgid "Applied on credit journal item"
msgstr ""

#. module: account
#: selection:tax.adjustments.wizard,adjustment_type:0
msgid "Applied on debit journal item"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_onboarding_invoice_layout_form
#: model_terms:ir.ui.view,arch_db:account.account_invoice_onboarding_sale_tax_form
#: model_terms:ir.ui.view,arch_db:account.setup_financial_year_opening_form
msgid "Apply"
msgstr "Primjeni"

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position__auto_apply
#: model:ir.model.fields,help:account.field_account_fiscal_position_template__auto_apply
msgid "Apply automatically this fiscal position."
msgstr "Primjeni automatski ovu fiskalnu poziciju."

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position__country_group_id
#: model:ir.model.fields,help:account.field_account_fiscal_position_template__country_group_id
msgid "Apply only if delivery or invoicing country match the group."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position__country_id
#: model:ir.model.fields,help:account.field_account_fiscal_position_template__country_id
msgid "Apply only if delivery or invoicing country match."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position__vat_required
#: model:ir.model.fields,help:account.field_account_fiscal_position_template__vat_required
msgid "Apply only if partner has a VAT number."
msgstr "Primjeni samo u slučaju da partner ima PDV broj."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Apply right VAT rates for digital products sold in EU"
msgstr ""

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "April"
msgstr "April"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_incoterms_view_search
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_search
#: model_terms:ir.ui.view,arch_db:account.view_account_position_filter
msgid "Archived"
msgstr "Arhivirano"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Ask for a Credit Note"
msgstr "Zatraži knjižno odobrenje"

#. module: account
#: selection:account.account.type,internal_group:0
msgid "Asset"
msgstr "Osnovna sredstva"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_search
msgid "Assets"
msgstr "Osnovna sredstva"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_asset
msgid "Assets Management"
msgstr "Upravljanje Sredstvima"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__account_ids
msgid "Associated Account Templates"
msgstr "Povezani prijedlozi konta"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__at_least_one_inbound
msgid "At Least One Inbound"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__at_least_one_outbound
msgid "At Least One Outbound"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
msgid "Attach a file"
msgstr "Zakači datoteku"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_attachment_count
#: model:ir.model.fields,field_description:account.field_account_invoice__message_attachment_count
#: model:ir.model.fields,field_description:account.field_account_payment__message_attachment_count
msgid "Attachment Count"
msgstr "Broj zakački"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__attachment_ids
msgid "Attachments"
msgstr "Prilozi"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__attachment_ids
msgid ""
"Attachments are linked to a document through model / res_id and to the "
"message through this field."
msgstr ""
"Zakačke su povezane sa dokumentom preko model / res_id i sa porukom putem "
"ovog polja."

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "August"
msgstr "Avgust"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__author_id
msgid "Author"
msgstr "Autor"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__author_id
msgid ""
"Author of the message. If not set, email_from may hold an email address that"
" did not match any partner."
msgstr ""
"Autor poruke. Ako nije postavljen, email_from može sadržavati adresu e-pošte"
" koja nije odgovarala niti jednom partneru."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__author_avatar
msgid "Author's avatar"
msgstr "Avatar autora"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Auto-Complete"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__vendor_bill_id
msgid "Auto-complete from a past bill."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Auto-detect"
msgstr "Automatska detekcija"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__auto_reconcile
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__auto_reconcile
msgid "Auto-validate"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Autocomplete Vendor Bills (OCR + AI)"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_invoice_extract
msgid "Automate Bill Processing"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Automate deferred revenues entries for multi-year contracts"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Automated Entries"
msgstr "Automatizovani unosi"

#. module: account
#: code:addons/account/models/company.py:407
#, python-format
msgid "Automatic Balancing Line"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_currency_rate_live
msgid "Automatic Currency Rates"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Automatic Import"
msgstr "Automatski uvoz"

#. module: account
#: code:addons/account/models/account_move.py:399
#, python-format
msgid "Automatic reversal of: %s"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__price_average
msgid "Average Price"
msgstr "Prosječna Cijena"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__user_currency_price_average
msgid "Average Price in Currency"
msgstr ""

#. module: account
#: code:addons/account/models/chart_template.py:410
#, python-format
msgid "BILL"
msgstr "Račun"

#. module: account
#: selection:res.partner,trust:0
msgid "Bad Debtor"
msgstr "Loš platioc"

#. module: account
#: selection:account.payment.term.line,value:0
#: model:ir.model.fields,field_description:account.field_account_move_line__balance
msgid "Balance"
msgstr "Saldo"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__balance_cash_basis
msgid "Balance Cash Basis"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_form
msgid "Balance Sheet"
msgstr "Bilans stanja"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__balance_end
msgid "Balance as calculated based on Opening Balance and transaction lines"
msgstr ""

#. module: account
#: selection:account.journal,type:0
#: code:addons/account/models/chart_template.py:340
#: model:ir.model.fields,field_description:account.field_account_journal__bank_id
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__bank_id
#: model:ir.model.fields,field_description:account.field_res_partner__bank_account_count
#, python-format
msgid "Bank"
msgstr "Banka"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Bank &amp; Cash"
msgstr "Banka i Gotovina"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__bank_account_id
#: model:ir.model.fields,field_description:account.field_account_invoice__partner_bank_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report__partner_bank_id
#: model:ir.model.fields,field_description:account.field_account_journal__bank_account_id
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_form
#: model_terms:ir.ui.view,arch_db:account.onboarding_bank_account_step
#: model_terms:ir.ui.view,arch_db:account.view_account_bank_journal_form
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Bank Account"
msgstr "Račun banke"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_bank_journal_form
msgid "Bank Account Name"
msgstr "Ime bankovnog računa"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__account_number
msgid "Bank Account Number"
msgstr "Broj bankovnog računa"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__partner_bank_id
msgid ""
"Bank Account Number to which the invoice will be paid. A Company bank "
"account if this is a Customer Invoice or Vendor Credit Note, otherwise a "
"Partner bank account number."
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_account_bank_journal_form
#: model:ir.model,name:account.model_res_partner_bank
#: model:ir.ui.menu,name:account.menu_action_account_bank_journal_form
#: model_terms:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Bank Accounts"
msgstr "Računi banke"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__bank_statements_source
msgid "Bank Feeds"
msgstr "Provizije banke"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__bank_bic
msgid "Bank Identifier Code"
msgstr "Identifikacijska šifra banke"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_yodlee
msgid "Bank Interface - Sync your bank feeds automatically"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__bank_journal_ids
msgid "Bank Journals"
msgstr "Dnevik izvoda iz banke"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Bank Operations"
msgstr "Bankovne operacije"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_action.js:17
#, python-format
msgid "Bank Reconciliation"
msgstr "Zatvaranje izvoda iz banke"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_tree
msgid "Bank Reconciliation Move Presets"
msgstr "Prijedlozi zatvaranja izvoda iz banke"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_search
msgid "Bank Reconciliation Move preset"
msgstr "Prijedlozi zatvaranja izvoda iz banke"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_bank_reconciliation_start
#: model:ir.model.fields,field_description:account.field_res_config_settings__account_bank_reconciliation_start
msgid "Bank Reconciliation Threshold"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_bank_statement
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_form
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_search
msgid "Bank Statement"
msgstr "Izvod banke"

#. module: account
#: code:addons/account/models/account_bank_statement.py:591
#, python-format
msgid "Bank Statement %s"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_bank_statement_cashbox
msgid "Bank Statement Cashbox"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_bank_statement_closebalance
msgid "Bank Statement Closing Balance"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "Stavka izvoda banke"

#. module: account
#: model:ir.actions.act_window,name:account.action_bank_statement_line
msgid "Bank Statement Lines"
msgstr "Stavke izvoda iz banke"

#. module: account
#: model:ir.actions.act_window,name:account.action_bank_statement_tree
#: model_terms:ir.ui.view,arch_db:account.view_account_bank_journal_form
msgid "Bank Statements"
msgstr "Izvodi banke"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line__bank_account_id
msgid "Bank account that was used in this transaction."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_setup_bank_manual_config__acc_type
msgid ""
"Bank account type: Normal or IBAN. Inferred from the bank account number."
msgstr ""

#. module: account
#: model:account.account.type,name:account.data_account_type_liquidity
msgid "Bank and Cash"
msgstr "Banka i Blagajna"

#. module: account
#: model:ir.model,name:account.model_account_setup_bank_manual_config
msgid "Bank setup manual config"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__statement_line_id
msgid "Bank statement line reconciled with this entry"
msgstr "Stavka izvoda iz banke je zatvorena sa ovom stavkom"

#. module: account
#: model:ir.actions.act_window,name:account.act_account_journal_2_account_bank_statement
msgid "Bank statements"
msgstr "Bankovni izvodi"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:36
#, python-format
msgid "Bank: Balance"
msgstr "Banka: Saldo"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_tax__base
msgid "Base"
msgstr "Osnova"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__tax_base_amount
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "Base Amount"
msgstr "Osnovica"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__cash_basis_base_account_id
#: model:ir.model.fields,field_description:account.field_account_tax_template__cash_basis_base_account_id
msgid "Base Tax Received Account"
msgstr ""

#. module: account
#: code:addons/account/models/company.py:19
#, python-format
msgid "Based on Customer"
msgstr ""

#. module: account
#: selection:account.tax,tax_exigibility:0
#: selection:account.tax.template,tax_exigibility:0
msgid "Based on Invoice"
msgstr "Bazirano na fakturi"

#. module: account
#: code:addons/account/models/company.py:19
#, python-format
msgid "Based on Invoice Number"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__tax_exigibility
#: model:ir.model.fields,help:account.field_account_tax_template__tax_exigibility
msgid ""
"Based on Invoice: the tax is due as soon as the invoice is validated.\n"
"Based on Payment: the tax is due as soon as the payment of the invoice is received."
msgstr ""

#. module: account
#: selection:account.tax,tax_exigibility:0
#: selection:account.tax.template,tax_exigibility:0
msgid "Based on Payment"
msgstr "Bazirano na uplati"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Batch Payments"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__belongs_to_company
msgid "Belong to the user's current company"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Bill"
msgstr "Račun"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_form
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_tree
msgid "Bill Date"
msgstr "Datum računa"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Bill lines"
msgstr "Stavke Računa"

#. module: account
#: model:res.groups,name:account.group_account_invoice
msgid "Billing"
msgstr "Računi"

#. module: account
#: model:res.groups,name:account.group_account_manager
msgid "Billing Manager"
msgstr "Upravitelj fakturisanja"

#. module: account
#: model:ir.actions.server,name:account.action_invoice_tree2
#: model:ir.ui.menu,name:account.menu_action_invoice_tree2
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Bills"
msgstr "Računi"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Bills Analysis"
msgstr "Analiza Računa"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Bills to Pay"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Bills to Validate"
msgstr ""

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:178
#, python-format
msgid "Bills to pay"
msgstr "Računi za plaćanje"

#. module: account
#: selection:res.partner,invoice_warn:0
msgid "Blocking Message"
msgstr "Blokiranje poruke"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_type__include_initial_balance
msgid "Bring Accounts Balance Forward"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.open_account_journal_dashboard_kanban
msgid "Browse available countries."
msgstr "Pregledaj dostupne države"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_budget
msgid "Budget Management"
msgstr "Upravljanje budžetom"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__name
msgid "Button Label"
msgstr "Oznaka dugmeta"

#. module: account
#: model:ir.filters,name:account.filter_invoice_country
msgid "By Country"
msgstr "Po državi"

#. module: account
#: model:ir.filters,name:account.filter_invoice_refund
msgid "By Credit Note"
msgstr "Po knjižnom odobrenju"

#. module: account
#: model:ir.filters,name:account.filter_invoice_product
msgid "By Product"
msgstr "Po proizvodu"

#. module: account
#: model:ir.filters,name:account.filter_invoice_product_category
msgid "By Product Category"
msgstr "Po kategoriji proizvoda"

#. module: account
#: model:ir.filters,name:account.filter_invoice_report_salespersons
#: model:ir.filters,name:account.filter_invoice_salespersons
msgid "By Salespersons"
msgstr "Po prodavač(ici)"

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position__active
msgid ""
"By unchecking the active field, you may hide a fiscal position without "
"deleting it."
msgstr ""
"Isključivanjem polja aktivno, možete sakriti fiskalnu poziciju bez da je "
"brišete."

#. module: account
#: model:ir.model.fields,help:account.field_account_incoterms__active
msgid ""
"By unchecking the active field, you may hide an INCOTERM you will not use."
msgstr ""

#. module: account
#: code:addons/account/models/chart_template.py:389
#: code:addons/account/models/chart_template.py:413
#, python-format
msgid "CABA"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "CAMT Import"
msgstr ""

#. module: account
#: model:account.incoterms,name:account.incoterm_CIP
msgid "CARRIAGE AND INSURANCE PAID TO"
msgstr ""

#. module: account
#: model:account.incoterms,name:account.incoterm_CPT
msgid "CARRIAGE PAID TO"
msgstr ""

#. module: account
#: model:account.incoterms,name:account.incoterm_CFR
msgid "COST AND FREIGHT"
msgstr ""

#. module: account
#: model:account.incoterms,name:account.incoterm_CIF
msgid "COST, INSURANCE AND FREIGHT"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "CSV Import"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__visible
msgid "Can be Visible?"
msgstr "Može biti vidljivo?"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_common_report_view
#: model_terms:ir.ui.view,arch_db:account.account_invoice_confirm_view
#: model_terms:ir.ui.view,arch_db:account.account_invoice_import_wizard_form_view
#: model_terms:ir.ui.view,arch_db:account.account_invoice_onboarding_sale_tax_form
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
#: model_terms:ir.ui.view,arch_db:account.account_unreconcile_view
#: model_terms:ir.ui.view,arch_db:account.cash_box_in_form
#: model_terms:ir.ui.view,arch_db:account.cash_box_out_form
#: model_terms:ir.ui.view,arch_db:account.setup_bank_account_wizard
#: model_terms:ir.ui.view,arch_db:account.setup_financial_year_opening_form
#: model_terms:ir.ui.view,arch_db:account.tax_adjustments_wizard
#: model_terms:ir.ui.view,arch_db:account.validate_account_move_view
#: model_terms:ir.ui.view,arch_db:account.view_account_bnk_stmt_cashbox
#: model_terms:ir.ui.view,arch_db:account.view_account_bnk_stmt_check
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_refund
#: model_terms:ir.ui.view,arch_db:account.view_account_move_reversal
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_from_invoices
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_invoice_form
msgid "Cancel"
msgstr "Otkaži"

#. module: account
#: selection:account.invoice.refund,filter_refund:0
msgid "Cancel: create credit note and reconcile"
msgstr "Otkaži: kreiraj knjižno odobrenje i zatvori fakturu"

#. module: account
#: selection:account.invoice,state:0 selection:account.invoice.report,state:0
#: selection:account.payment,state:0
msgid "Cancelled"
msgstr "Otkazan"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "Cancelled Invoice"
msgstr "Otkazana faktura"

#. module: account
#: code:addons/account/wizard/account_invoice_refund.py:56
#, python-format
msgid ""
"Cannot create a credit note for the invoice which is already reconciled, "
"invoice should be unreconciled first, then only you can add credit note for "
"this invoice."
msgstr ""
"Ne može se kreirati knjižno odobrenje za fakture koje su već zatvorene, prvo"
" morate ukloniti zatvaranje, pa onda možete dodati knjižno odobrenje za ovu "
"fakturu."

#. module: account
#: code:addons/account/wizard/account_invoice_refund.py:54
#, python-format
msgid "Cannot create credit note for the draft/cancelled invoice."
msgstr ""
"Nije moguće kreirati knjižno odobrenje za fakture u pripremi ili otkazane."

#. module: account
#: code:addons/account/models/account_move.py:358
#, python-format
msgid "Cannot create moves for different companies."
msgstr "Nije moguće kreirati knjiženja za različite kompanije."

#. module: account
#: code:addons/account/models/account_move.py:390
#, python-format
msgid "Cannot create unbalanced journal entry."
msgstr "Nije moguće kreirati dnevnički zapis čiji saldo nije u balansu."

#. module: account
#: code:addons/account/models/account_invoice.py:757
#, python-format
msgid ""
"Cannot find a chart of accounts for this company, You should configure it. \n"
"Please go to Account Configuration."
msgstr ""
"Nije bilo moguće pronaći kontni plan za ovu kompaniju. Trebate je konfigurisati.\n"
"Molimo da odete na konfiguraciju računovodstva."

#. module: account
#: code:addons/account/models/account.py:111
#: code:addons/account/models/chart_template.py:151
#, python-format
msgid "Cannot generate an unused account code."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:756
#, python-format
msgid ""
"Cannot generate an unused journal code. Please fill the 'Shortcode' field."
msgstr ""

#. module: account
#: selection:account.journal,type:0
#: code:addons/account/models/chart_template.py:340
#, python-format
msgid "Cash"
msgstr "Gotovina"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__tax_exigibility
msgid "Cash Basis"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__tax_cash_basis_journal_id
msgid "Cash Basis Journal"
msgstr ""

#. module: account
#: code:addons/account/models/chart_template.py:413
#, python-format
msgid "Cash Basis Tax Journal"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_cash_box_in
msgid "Cash Box In"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_cash_box_out
msgid "Cash Box Out"
msgstr ""

#. module: account
#: code:addons/account/models/account_bank_statement.py:212
#: model:ir.actions.act_window,name:account.action_view_account_bnk_stmt_cashbox
#, python-format
msgid "Cash Control"
msgstr "Kontrola gotovine"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Cash Operations"
msgstr "Blagajničke operacije"

#. module: account
#: model:ir.actions.act_window,name:account.action_view_bank_statement_tree
msgid "Cash Registers"
msgstr "Kase"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__group_cash_rounding
msgid "Cash Rounding"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__cash_rounding_id
msgid "Cash Rounding Method"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.rounding_list_action
#: model:ir.ui.menu,name:account.menu_action_rounding_form_view
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Cash Roundings"
msgstr ""

#. module: account
#: code:addons/account/models/account_bank_statement.py:187
#, python-format
msgid "Cash difference observed during the counting (%s)"
msgstr ""

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:34
#, python-format
msgid "Cash: Balance"
msgstr "Gotovina: Saldo"

#. module: account
#: model:ir.model,name:account.model_account_cashbox_line
msgid "CashBox Line"
msgstr "Stavka blagajne"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cashbox_line__cashbox_id
msgid "Cashbox"
msgstr "Blagajna"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox__cashbox_lines_ids
msgid "Cashbox Lines"
msgstr "Stavke blagajne"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__property_account_expense_categ_id
msgid "Category of Expense Account"
msgstr "Kategorija konta troška"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__property_account_income_categ_id
msgid "Category of Income Account"
msgstr "Kategorija konta dobiti"

#. module: account
#: model:ir.model.fields,help:account.field_account_abstract_payment__writeoff_label
#: model:ir.model.fields,help:account.field_account_payment__writeoff_label
#: model:ir.model.fields,help:account.field_account_register_payments__writeoff_label
msgid "Change label of the counterpart that will hold the payment difference"
msgstr ""

#. module: account
#: code:addons/account/controllers/portal.py:105
#, python-format
msgid ""
"Changing VAT number is not allowed once invoices have been issued for your "
"account. Please contact us directly for this operation."
msgstr ""

#. module: account
#: code:addons/account/controllers/portal.py:111
#, python-format
msgid ""
"Changing your company name is not allowed once invoices have been issued for"
" your account. Please contact us directly for this operation."
msgstr ""

#. module: account
#: code:addons/account/controllers/portal.py:108
#, python-format
msgid ""
"Changing your name is not allowed once invoices have been issued for your "
"account. Please contact us directly for this operation."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__channel_ids
msgid "Channels"
msgstr "Kanali"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_template__chart_template_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__chart_template_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__chart_template_id
#: model:ir.model.fields,field_description:account.field_account_tax_template__chart_template_id
#: model:ir.model.fields,field_description:account.field_res_company__chart_template_id
msgid "Chart Template"
msgstr "Predložak plana"

#. module: account
#: model:ir.actions.act_window,name:account.open_account_charts_modules
msgid "Chart Templates"
msgstr "Predlošci plana"

#. module: account
#: code:addons/account/models/company.py:320
#: model:ir.actions.act_window,name:account.action_account_form
#: model:ir.ui.menu,name:account.menu_action_account_form
#: model_terms:ir.ui.view,arch_db:account.onboarding_chart_of_account_step
#, python-format
msgid "Chart of Accounts"
msgstr "Kontni plan"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_chart_template_form
#: model_terms:ir.ui.view,arch_db:account.view_account_chart_template_tree
msgid "Chart of Accounts Template"
msgstr "Predložak kontnog plana"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_chart_template_form
msgid "Chart of Accounts Templates"
msgstr "Predlošci računskog plana"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_chart_of_account_step
msgid "Chart of account set."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_list
msgid "Chart of accounts"
msgstr "Kontni plan"

#. module: account
#: model:ir.actions.act_window,name:account.action_view_account_bnk_stmt_check
msgid "Check Closing Balance"
msgstr "Provjeri Saldo prilikom zatvaranja"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__is_difference_zero
msgid "Check if difference is zero."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_account__reconcile
msgid ""
"Check this box if this account allows invoices & payments matching of "
"journal items."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__refund_sequence
msgid ""
"Check this box if you don't want to share the same sequence for invoices and"
" credit notes made from this journal"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__update_posted
msgid ""
"Check this box if you want to allow the cancellation the entries related to "
"this journal or of the invoice related to this journal"
msgstr ""
"Označite ako želite dopustiti naknadno otkazivanje proknjiženih (potvrđenih)"
" dnevničkih zapisa ili računa ovog dnevnika."

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__price_include
#: model:ir.model.fields,help:account.field_account_tax_template__price_include
msgid ""
"Check this if the price you use on the product and invoices includes this "
"tax."
msgstr ""
"Odaberite ovo ako cijena koju koristite na proizvodu i računima uključuje "
"ovaj porez."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_template__reconcile
msgid ""
"Check this option if you want the user to reconcile entries in this account."
msgstr ""
"Označite ovu opciju ako želite da korisnik izjednači unose u ovaj konto."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Checks"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__child_ids
msgid "Child Messages"
msgstr "Podređene poruke"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__children_tax_ids
#: model:ir.model.fields,field_description:account.field_account_tax_template__children_tax_ids
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_template_form
#: model_terms:ir.ui.view,arch_db:account.view_tax_form
msgid "Children Taxes"
msgstr "Podporezi"

#. module: account
#: code:addons/account/models/chart_template.py:349
#, python-format
msgid "Choose Accounting Template"
msgstr "Izaberi prijedlog kontnog plana"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_onboarding_sale_tax_form
#: model_terms:ir.ui.view,arch_db:account.onboarding_sale_tax_step
msgid "Choose a default sales tax for your products."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:137
#, python-format
msgid "Choose counterpart or Create Write-off"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_refund__filter_refund
msgid ""
"Choose how you want to credit this invoice. You cannot Modify and Cancel if "
"the invoice is already reconciled"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.actions_account_fiscal_year
msgid "Click here to create a new fiscal year."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/tour.js:19
#, python-format
msgid "Click to <b>send the invoice by email.</b>"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/tour.js:23
#, python-format
msgid "Click to <b>send the invoice.</b>"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/tour.js:14
#, python-format
msgid ""
"Click to <b>validate your invoice.</b> A reference will be assigned to this "
"invoice and you will not be able to modify it anymore."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:17
#, python-format
msgid "Click to Rename"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:341
#, python-format
msgid "Close"
msgstr "Zatvori"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:66
#, python-format
msgid "Close statement"
msgstr "Zatvori izvod"

#. module: account
#: selection:res.company,account_dashboard_onboarding_state:0
#: selection:res.company,account_invoice_onboarding_state:0
msgid "Closed"
msgstr "Zatvoreno"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__date_done
msgid "Closed On"
msgstr "Zatvoren"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment__payment_method_code
#: model:ir.model.fields,field_description:account.field_account_account__code
#: model:ir.model.fields,field_description:account.field_account_account_template__code
#: model:ir.model.fields,field_description:account.field_account_analytic_line__code
#: model:ir.model.fields,field_description:account.field_account_incoterms__code
#: model:ir.model.fields,field_description:account.field_account_payment__payment_method_code
#: model:ir.model.fields,field_description:account.field_account_payment_method__code
#: model:ir.model.fields,field_description:account.field_account_register_payments__payment_method_code
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__new_journal_code
msgid "Code"
msgstr "Šifra"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_group__code_prefix
msgid "Code Prefix"
msgstr "Prefiks šifre"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cashbox_line__coin_value
msgid "Coin/Bill Value"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Collect customer payments in one-click using Euro SEPA Service"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Collect information and produce statistics on the trade in goods in Europe "
"with intrastat."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag__color
#: model:ir.model.fields,field_description:account.field_account_journal__color
msgid "Color Index"
msgstr "Indeks boje"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__commercial_partner_id
#: model:ir.model.fields,help:account.field_account_invoice_report__commercial_partner_id
msgid "Commercial Entity"
msgstr "Komercijalni entitet"

#. module: account
#: code:addons/account/models/account_invoice.py:519
#, python-format
msgid "Commercial partner and vendor account owners must be identical."
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_common_journal_report
msgid "Common Journal Report"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_account_common_menu
msgid "Common Report"
msgstr "Izvještaji"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__invoice_reference_type
msgid "Communication"
msgstr "Komunikacija"

#. module: account
#: model:ir.model,name:account.model_res_company
msgid "Companies"
msgstr "Kompanije"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__ref_company_ids
msgid "Companies that refers to partner"
msgstr "Firme koje se vežu sa partnerom"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__company_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement__company_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__company_id
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__company_id
#: model:ir.model.fields,field_description:account.field_account_common_report__company_id
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__company_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__company_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_year__company_id
#: model:ir.model.fields,field_description:account.field_account_invoice__company_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line__company_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report__company_id
#: model:ir.model.fields,field_description:account.field_account_invoice_tax__company_id
#: model:ir.model.fields,field_description:account.field_account_journal__company_id
#: model:ir.model.fields,field_description:account.field_account_move__company_id
#: model:ir.model.fields,field_description:account.field_account_move_line__company_id
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__company_id
#: model:ir.model.fields,field_description:account.field_account_payment__company_id
#: model:ir.model.fields,field_description:account.field_account_payment_term__company_id
#: model:ir.model.fields,field_description:account.field_account_print_journal__company_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__company_id
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__company_id
#: model:ir.model.fields,field_description:account.field_account_tax__company_id
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Company"
msgstr "Kompanija"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__company_currency_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line__company_currency_id
#: model:ir.model.fields,field_description:account.field_account_move_line__company_currency_id
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__company_currency_id
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__company_currency_id
msgid "Company Currency"
msgstr "Valuta preduzeća"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__has_chart_of_accounts
msgid "Company has a chart of accounts"
msgstr "Kompanija ima kontni plan"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__company_id
#: model:ir.model.fields,help:account.field_account_bank_statement_line__company_id
#: model:ir.model.fields,help:account.field_account_journal__company_id
#: model:ir.model.fields,help:account.field_account_move__company_id
#: model:ir.model.fields,help:account.field_account_payment__company_id
msgid "Company related to this journal"
msgstr "Kompanija za koju se vodi ovaj dnevnik"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__complete_tax_set
msgid "Complete Set of Taxes"
msgstr "Kompletan popis poreza"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__composer_id
msgid "Composer"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__composition_mode
msgid "Composition mode"
msgstr "Mod sastavljanja"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Compute tax rates based on U.S. ZIP codes"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__counterpart
msgid ""
"Compute the counter part accounts of this journal item for this journal "
"entry. This can be needed in reports."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__balance_end
msgid "Computed Balance"
msgstr "Izračunati saldo"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Conditions on Bank Statement Line"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_configuration
msgid "Configuration"
msgstr "Konfiguracija"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.onboarding_fiscal_year_step
msgid "Configure"
msgstr "Konfiguriši"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/section_and_note_fields_backend.js:102
#, python-format
msgid "Configure a product"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_open_account_onboarding_invoice_layout
msgid "Configure your document layout"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_bnk_stmt_cashbox
#: model_terms:ir.ui.view,arch_db:account.view_account_bnk_stmt_check
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_form
msgid "Confirm"
msgstr "Portvrdi"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_invoice_confirm
#: model_terms:ir.ui.view,arch_db:account.account_invoice_confirm_view
msgid "Confirm Draft Invoices"
msgstr "Potvrdite fakture u pripremi"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_confirm_view
msgid "Confirm Invoices"
msgstr "Potvrdi fakture"

#. module: account
#: model:ir.actions.server,name:account.action_account_confirm_payments
msgid "Confirm Payments"
msgstr "Potvrdite uplate"

#. module: account
#: model:ir.model,name:account.model_account_invoice_confirm
msgid "Confirm the selected invoices"
msgstr "Potvrdi odabrane fakture"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_search
msgid "Confirmed"
msgstr "Potvrđeno"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_bnk_stmt_check
msgid ""
"Confirming this will create automatically a journal entry with the "
"difference in the profit/loss account set on the cash journal."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:54
#, python-format
msgid "Congrats, you're all done!"
msgstr "Čestitamo, sve ste završili!"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_onboarding_panel
msgid "Congratulations! You are all set."
msgstr ""

#. module: account
#: model:ir.model,name:account.model_res_partner
#: model_terms:ir.ui.view,arch_db:account.portal_invoice_page
msgid "Contact"
msgstr "Kontakt"

#. module: account
#: selection:account.reconcile.model,match_label:0
#: selection:account.reconcile.model.template,match_label:0
msgid "Contains"
msgstr "Sadrži"

#. module: account
#: model:ir.model,name:account.model_account_abstract_payment
msgid ""
"Contains the logic shared between models which allows to register payments"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__body
msgid "Contents"
msgstr "Sadržaji"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__contracts_count
msgid "Contracts Count"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Control-Access"
msgstr "Kontrola pristupa"

#. module: account
#: code:addons/account/wizard/account_invoice_refund.py:103
#, python-format
msgid ""
"Correction of <a href=# data-oe-model=account.invoice data-oe-"
"id=%d>%s</a><br>Reason: %s"
msgstr ""

#. module: account
#: model:account.account.type,name:account.data_account_type_direct_costs
msgid "Cost of Revenue"
msgstr "Trošak prihoda"

#. module: account
#: code:addons/account/models/chart_template.py:192
#, python-format
msgid ""
"Could not install new chart of account as there are already accounting "
"entries existing."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__counterpart
msgid "Counterpart"
msgstr "Protustavka"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__account_id
msgid "Counterpart Account"
msgstr "Protukonto"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__country_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__country_id
msgid "Country"
msgstr "Država"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__country_group_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__country_group_id
msgid "Country Group"
msgstr "Grupa zemalja"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.setup_bank_account_wizard
msgid "Create"
msgstr "Kreiraj"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__create_or_link_option
msgid "Create Or Link Option"
msgstr ""

#. module: account
#: code:addons/account/models/company.py:276
#, python-format
msgid "Create a Bank Account"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_bank_journal_form
msgid "Create a bank account"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_invoice_out_refund
msgid "Create a credit note"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_invoice_tree1
msgid "Create a customer invoice"
msgstr ""

#. module: account
#: selection:account.invoice.refund,filter_refund:0
msgid "Create a draft credit note"
msgstr "Kreiraj knjižno odobrenje u pripremi"

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_move_journal_line
msgid "Create a journal entry"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_group_tree
msgid "Create a new account group"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_view_bank_statement_tree
msgid "Create a new cash log"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_fiscal_position_form
#: model_terms:ir.actions.act_window,help:account.action_account_fiscal_position_template_form
msgid "Create a new fiscal position"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_incoterms_tree
msgid "Create a new incoterm"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.product_product_action_purchasable
msgid "Create a new purchasable product"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_reconcile_model
msgid "Create a new reconciliation model"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.product_product_action_sellable
msgid "Create a new sellable product"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_tax_form
msgid "Create a new tax"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_invoice_in_refund
msgid "Create a vendor credit note"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.tax_adjustments_wizard
msgid "Create and post move"
msgstr "Kreiraj i proikniži kretanja"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:281
#, python-format
msgid "Create cash statement"
msgstr "Kreiraj nalog blagajni"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:267
#, python-format
msgid "Create invoice/bill"
msgstr "Kreiraj Račun/Fakturu"

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_invoice_tree1
msgid ""
"Create invoices, register payments and keep track of the discussions with "
"your customers."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:174
#, python-format
msgid "Create model"
msgstr "Kreiraj model"

#. module: account
#: selection:account.setup.bank.manual.config,create_or_link_option:0
msgid "Create new journal"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.rounding_list_action
msgid "Create the first cash rounding"
msgstr "Kreirajte prvo zaokruživanje gotovine"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__create_uid
#: model:ir.model.fields,field_description:account.field_account_account_tag__create_uid
#: model:ir.model.fields,field_description:account.field_account_account_template__create_uid
#: model:ir.model.fields,field_description:account.field_account_account_type__create_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement__create_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox__create_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance__create_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__create_uid
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__create_uid
#: model:ir.model.fields,field_description:account.field_account_cashbox_line__create_uid
#: model:ir.model.fields,field_description:account.field_account_chart_template__create_uid
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__create_uid
#: model:ir.model.fields,field_description:account.field_account_common_report__create_uid
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account__create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template__create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax__create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template__create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_year__create_uid
#: model:ir.model.fields,field_description:account.field_account_full_reconcile__create_uid
#: model:ir.model.fields,field_description:account.field_account_group__create_uid
#: model:ir.model.fields,field_description:account.field_account_incoterms__create_uid
#: model:ir.model.fields,field_description:account.field_account_invoice__create_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm__create_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_import_wizard__create_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_line__create_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_refund__create_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_send__create_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_tax__create_uid
#: model:ir.model.fields,field_description:account.field_account_journal__create_uid
#: model:ir.model.fields,field_description:account.field_account_move__create_uid
#: model:ir.model.fields,field_description:account.field_account_move_line__create_uid
#: model:ir.model.fields,field_description:account.field_account_move_reversal__create_uid
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__create_uid
#: model:ir.model.fields,field_description:account.field_account_payment__create_uid
#: model:ir.model.fields,field_description:account.field_account_payment_method__create_uid
#: model:ir.model.fields,field_description:account.field_account_payment_term__create_uid
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__create_uid
#: model:ir.model.fields,field_description:account.field_account_print_journal__create_uid
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__create_uid
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__create_uid
#: model:ir.model.fields,field_description:account.field_account_register_payments__create_uid
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__create_uid
#: model:ir.model.fields,field_description:account.field_account_tax__create_uid
#: model:ir.model.fields,field_description:account.field_account_tax_group__create_uid
#: model:ir.model.fields,field_description:account.field_account_tax_template__create_uid
#: model:ir.model.fields,field_description:account.field_account_unreconcile__create_uid
#: model:ir.model.fields,field_description:account.field_cash_box_in__create_uid
#: model:ir.model.fields,field_description:account.field_cash_box_out__create_uid
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__create_uid
#: model:ir.model.fields,field_description:account.field_validate_account_move__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__create_date
#: model:ir.model.fields,field_description:account.field_account_account_tag__create_date
#: model:ir.model.fields,field_description:account.field_account_account_template__create_date
#: model:ir.model.fields,field_description:account.field_account_account_type__create_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement__create_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox__create_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance__create_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__create_date
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__create_date
#: model:ir.model.fields,field_description:account.field_account_cashbox_line__create_date
#: model:ir.model.fields,field_description:account.field_account_chart_template__create_date
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__create_date
#: model:ir.model.fields,field_description:account.field_account_common_report__create_date
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account__create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template__create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax__create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template__create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_year__create_date
#: model:ir.model.fields,field_description:account.field_account_full_reconcile__create_date
#: model:ir.model.fields,field_description:account.field_account_group__create_date
#: model:ir.model.fields,field_description:account.field_account_incoterms__create_date
#: model:ir.model.fields,field_description:account.field_account_invoice__create_date
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm__create_date
#: model:ir.model.fields,field_description:account.field_account_invoice_import_wizard__create_date
#: model:ir.model.fields,field_description:account.field_account_invoice_line__create_date
#: model:ir.model.fields,field_description:account.field_account_invoice_refund__create_date
#: model:ir.model.fields,field_description:account.field_account_invoice_send__create_date
#: model:ir.model.fields,field_description:account.field_account_invoice_tax__create_date
#: model:ir.model.fields,field_description:account.field_account_journal__create_date
#: model:ir.model.fields,field_description:account.field_account_move__create_date
#: model:ir.model.fields,field_description:account.field_account_move_line__create_date
#: model:ir.model.fields,field_description:account.field_account_move_reversal__create_date
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__create_date
#: model:ir.model.fields,field_description:account.field_account_payment__create_date
#: model:ir.model.fields,field_description:account.field_account_payment_method__create_date
#: model:ir.model.fields,field_description:account.field_account_payment_term__create_date
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__create_date
#: model:ir.model.fields,field_description:account.field_account_print_journal__create_date
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__create_date
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__create_date
#: model:ir.model.fields,field_description:account.field_account_register_payments__create_date
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__create_date
#: model:ir.model.fields,field_description:account.field_account_tax__create_date
#: model:ir.model.fields,field_description:account.field_account_tax_group__create_date
#: model:ir.model.fields,field_description:account.field_account_tax_template__create_date
#: model:ir.model.fields,field_description:account.field_account_unreconcile__create_date
#: model:ir.model.fields,field_description:account.field_cash_box_in__create_date
#: model:ir.model.fields,field_description:account.field_cash_box_out__create_date
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__create_date
#: model:ir.model.fields,field_description:account.field_validate_account_move__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__credit
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "Credit"
msgstr "Potraživanje"

#. module: account
#: model:account.account.type,name:account.data_account_type_credit_card
msgid "Credit Card"
msgstr "Kreditna kartica"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__credit_cash_basis
msgid "Credit Cash Basis"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_refund__filter_refund
msgid "Credit Method"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__credit_move_id
msgid "Credit Move"
msgstr ""

#. module: account
#: code:addons/account/models/account_invoice.py:486
#: code:addons/account/models/account_invoice.py:1316
#: model:ir.actions.act_window,name:account.action_account_invoice_refund
#: model:ir.model,name:account.model_account_invoice_refund
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_refund
#, python-format
msgid "Credit Note"
msgstr "Knjižno odobrenje"

#. module: account
#: code:addons/account/models/account_invoice.py:487
#, python-format
msgid "Credit Note - %s"
msgstr "Knjižno odobrenje - %s"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Credit Note Bill"
msgstr "Račun knjižnog odobrenja"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_refund__date_invoice
msgid "Credit Note Date"
msgstr "Datum knjižnog odobrenja"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__refund_sequence_id
msgid "Credit Note Entry Sequence"
msgstr "Sekvenca zapisa knjižnog odobrenja"

#. module: account
#: model:ir.actions.act_window,name:account.action_invoice_out_refund
#: model:ir.ui.menu,name:account.menu_action_invoice_out_refund
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Credit Notes"
msgstr "Knjižna odobrenja"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__refund_sequence_number_next
msgid "Credit Notes: Next Number"
msgstr "Knjižna odobrenja: Sljedeći broj"

#. module: account
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__credit_account_id
msgid "Credit account"
msgstr "Potražni konto"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__matched_credit_ids
msgid "Credit journal items that are matched with this journal item."
msgstr ""

#. module: account
#: model:ir.ui.menu,name:account.menu_action_currency_form
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Currencies"
msgstr "Valute"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment__currency_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement__currency_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__currency_id
#: model:ir.model.fields,field_description:account.field_account_chart_template__currency_id
#: model:ir.model.fields,field_description:account.field_account_invoice__currency_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line__currency_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report__currency_id
#: model:ir.model.fields,field_description:account.field_account_invoice_tax__currency_id
#: model:ir.model.fields,field_description:account.field_account_journal__currency_id
#: model:ir.model.fields,field_description:account.field_account_move__currency_id
#: model:ir.model.fields,field_description:account.field_account_move_line__currency_id
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__currency_id
#: model:ir.model.fields,field_description:account.field_account_payment__currency_id
#: model:ir.model.fields,field_description:account.field_account_register_payments__currency_id
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__currency_id
#: model:ir.model.fields,field_description:account.field_res_config_settings__currency_id
#: model:ir.model.fields,field_description:account.field_res_partner__currency_id
#: model_terms:ir.ui.view,arch_db:account.report_journal
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
msgid "Currency"
msgstr "Valuta"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__currency_rate
msgid "Currency Rate"
msgstr "Kursna lista"

#. module: account
#: code:addons/account/models/account_move.py:1429
#: code:addons/account/models/account_move.py:1441
#, python-format
msgid "Currency exchange rate difference"
msgstr "Razlika u razmjeni valute"

#. module: account
#: model:account.account.type,name:account.data_account_type_current_assets
#: model_terms:ir.ui.view,arch_db:account.view_account_form
msgid "Current Assets"
msgstr "Trenutna imovina"

#. module: account
#: model:account.account.type,name:account.data_account_type_current_liabilities
msgid "Current Liabilities"
msgstr "Trenutna dugovanja"

#. module: account
#: model:account.account.type,name:account.data_unaffected_earnings
msgid "Current Year Earnings"
msgstr "Trenutna godišnja zarada"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__starred
msgid "Current user has a starred notification linked to this message"
msgstr "Trenutni korisnik ima označenu obavijest povezanu sa ovom porukom."

#. module: account
#: selection:account.abstract.payment,partner_type:0
#: selection:account.payment,partner_type:0
#: selection:account.register.payments,partner_type:0
#: model_terms:ir.ui.view,arch_db:account.invoice_form
#: model_terms:ir.ui.view,arch_db:account.invoice_tree
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_tree
msgid "Customer"
msgstr "Kupac"

#. module: account
#: selection:account.invoice,type:0 selection:account.invoice.report,type:0
#: code:addons/account/models/account_payment.py:769
#, python-format
msgid "Customer Credit Note"
msgstr "Knjižno odobrenje kupca"

#. module: account
#: selection:account.invoice,type:0 selection:account.invoice.report,type:0
msgid "Customer Invoice"
msgstr "Faktura kupca"

#. module: account
#: code:addons/account/models/chart_template.py:409
#, python-format
msgid "Customer Invoices"
msgstr "Fakture kupca"

#. module: account
#: code:addons/account/models/account_payment.py:767
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#, python-format
msgid "Customer Payment"
msgstr "Plaćanje kupca"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__property_payment_term_id
msgid "Customer Payment Terms"
msgstr "Uslovi plaćanja kupca"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Customer Payments"
msgstr "Plaćanja kupca"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__access_url
msgid "Customer Portal URL"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_product_template__taxes_id
msgid "Customer Taxes"
msgstr "Porezi kupca"

#. module: account
#: model:ir.ui.menu,name:account.menu_account_customer
#: model:ir.ui.menu,name:account.menu_finance_receivables
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Customers"
msgstr "Kupci"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_invoice_layout_step
msgid "Customize"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_invoice_layout_step
msgid "Customize the look of your invoices."
msgstr ""

#. module: account
#: model:account.incoterms,name:account.incoterm_DAF
msgid "DELIVERED AT FRONTIER"
msgstr ""

#. module: account
#: model:account.incoterms,name:account.incoterm_DAP
msgid "DELIVERED AT PLACE"
msgstr ""

#. module: account
#: model:account.incoterms,name:account.incoterm_DAT
msgid "DELIVERED AT TERMINAL"
msgstr ""

#. module: account
#: model:account.incoterms,name:account.incoterm_DDP
msgid "DELIVERED DUTY PAID"
msgstr ""

#. module: account
#: model:account.incoterms,name:account.incoterm_DDU
msgid "DELIVERED DUTY UNPAID"
msgstr ""

#. module: account
#: model:account.incoterms,name:account.incoterm_DEQ
msgid "DELIVERED EX QUAY"
msgstr ""

#. module: account
#: model:account.incoterms,name:account.incoterm_DES
msgid "DELIVERED EX SHIP"
msgstr ""

#. module: account
#: selection:account.cash.rounding,rounding_method:0
msgid "DOWN"
msgstr ""

#. module: account
#. openerp-web
#: selection:account.print.journal,sort_selection:0
#: code:addons/account/static/src/xml/account_reconciliation.xml:287
#: code:addons/account/static/src/xml/account_reconciliation.xml:302
#: model:ir.model.fields,field_description:account.field_account_bank_statement__date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__date
#: model:ir.model.fields,field_description:account.field_account_invoice_send__date
#: model:ir.model.fields,field_description:account.field_account_move__date
#: model:ir.model.fields,field_description:account.field_account_move_line__date
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__date
#: model_terms:ir.ui.view,arch_db:account.report_journal
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_search
#, python-format
msgid "Date"
msgstr "Datum"

#. module: account
#: model:ir.model.fields,help:account.field_res_company__account_opening_date
msgid ""
"Date at which the opening entry of this company's accounting has been "
"posted."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_financial_year_op__opening_date
msgid ""
"Date from which the accounting is managed in Odoo. It is the date of the "
"opening entry."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move__reverse_date
msgid "Date of the reverse accounting entry."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:56
#, python-format
msgid "Date:"
msgstr "Datum:"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
msgid "Dates"
msgstr "Datumi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__day_of_the_month
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_line_tree
msgid "Day of the month"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_term_line__day_of_the_month
msgid ""
"Day of the month on which the invoice must come to its term. If zero or "
"negative, this value will be ignored, and no specific day will be set. If "
"greater than the last day of a month, this number will instead select the "
"last day of this month."
msgstr ""

#. module: account
#: code:addons/account/models/company.py:51
#, python-format
msgid ""
"Dear Sir/Madam,\n"
"\n"
"Our records indicate that some payments on your account are still due. Please find details below.\n"
"If the amount has already been paid, please disregard this notice. Otherwise, please forward us the total amount stated below.\n"
"If you have any queries regarding your account, Please contact us.\n"
"\n"
"Thank you in advance for your cooperation.\n"
"Best Regards,"
msgstr ""
"Poštovani,\n"
"\n"
"uvidom u našu evidenciju utvrđeno je da imate otvorene dospjele stavke. Detalji su u nastavku.\n"
"Ukoliko ste iznos podmirili, molimo Vas da zanemarite ovu obavijest. U suprotnom Vas molimo da podmirite svoje dugovanje.\n"
"Ukoliko imate bilo kakvih pitanja ili nejasnoća, budite slobodni kontaktirati nas.\n"
"\n"
"Unaprijed zahvaljujem na Vašoj suradnji.\n"
"Srdačan pozdrav,"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__debit
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "Debit"
msgstr "Dugovanje"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__debit_cash_basis
msgid "Debit Cash Basis"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__debit_move_id
msgid "Debit Move"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__debit_account_id
msgid "Debit account"
msgstr "Dugovni konto"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__matched_debit_ids
msgid "Debit journal items that are matched with this journal item."
msgstr ""

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "December"
msgstr "Decembar"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__refund_sequence
msgid "Dedicated Credit Note Sequence"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__invoice_reference_type
msgid "Default Communication Type"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__default_credit_account_id
msgid "Default Credit Account"
msgstr "Zadano konto potraživanja"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__default_debit_account_id
msgid "Default Debit Account"
msgstr "Zadani dugovni konto"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_purchase_tax_id
#: model:ir.model.fields,field_description:account.field_res_config_settings__purchase_tax_id
msgid "Default Purchase Tax"
msgstr "Uobičajen porez nabavke"

#. module: account
#: model:ir.model.fields,help:account.field_res_config_settings__invoice_reference_type
msgid "Default Reference Type on Invoices."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_sale_tax_id
#: model:ir.model.fields,field_description:account.field_res_config_settings__sale_tax_id
msgid "Default Sale Tax"
msgstr "Zadani porez prodaje"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Default Sending Options"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__tax_ids
#: model:ir.model.fields,field_description:account.field_account_account_template__tax_ids
#: model_terms:ir.ui.view,arch_db:account.view_account_chart_template_form
#: model_terms:ir.ui.view,arch_db:account.view_account_template_form
msgid "Default Taxes"
msgstr "Zadani porezi"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__incoterm_id
msgid "Default incoterm"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Default payment communication on customer invoices"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Default taxes applied to local transactions"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_product_template__supplier_taxes_id
msgid "Default taxes used when buying the product."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_product_template__taxes_id
msgid "Default taxes used when selling the product."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Deferred Revenues Management"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_type_form
msgid "Define a new account type"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Define the smallest coinage of the currency used to pay by cash."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_fiscal_year_step
msgid "Define your fiscal years opening &amp; closing dates."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__bank_statements_source
msgid "Defines how the bank statements will be registered"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__cash_rounding_id
msgid ""
"Defines the smallest coinage of the currency that can be used to pay by "
"cash."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_template_form
#: model_terms:ir.ui.view,arch_db:account.view_tax_form
msgid "Definition"
msgstr "Definicija"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__trust
msgid "Degree of trust you have in this debtor"
msgstr "Nivo povjerenja koji imate prema ovom kupcu"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__auto_delete
msgid "Delete Emails"
msgstr "Obriši email-ove"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__auto_delete_message
msgid "Delete Message Copy"
msgstr "Obriši kopiju poruke"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__auto_delete
msgid "Delete sent emails (mass mailing only)"
msgstr "Obriši poslate email-ove (samo masovna epošta)"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__deprecated
msgid "Deprecated"
msgstr "Zastarjelo"

#. module: account
#: model:account.account.type,name:account.data_account_type_depreciation
#: model_terms:ir.ui.view,arch_db:account.view_account_form
msgid "Depreciation"
msgstr "Amortizacija"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.cash_box_out_form
msgid "Describe why you take money from the cash register:"
msgstr "Opišite kad uzimate novac iz blagajne :"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:305
#: model:ir.model.fields,field_description:account.field_account_account_type__note
#: model:ir.model.fields,field_description:account.field_account_invoice_line__name
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
#: model_terms:ir.ui.view,arch_db:account.view_account_type_form
#: model_terms:ir.ui.view,arch_db:account.view_invoice_line_form
#, python-format
msgid "Description"
msgstr "Opis"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term__note
msgid "Description on the Invoice"
msgstr "Opis računa"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__destination_account_id
msgid "Destination Account"
msgstr "Odredišni konto"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__auto_apply
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__auto_apply
msgid "Detect Automatically"
msgstr "Detektuj automatski"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__type_tax_use
#: model:ir.model.fields,help:account.field_account_tax_template__type_tax_use
msgid ""
"Determines where the tax is selectable. Note : 'None' means a tax can't be "
"used by itself, however it can still be used in a group. 'adjustment' is "
"used to perform tax adjustment."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__difference
msgid "Difference"
msgstr "Razlika"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment__writeoff_account_id
#: model:ir.model.fields,field_description:account.field_account_payment__writeoff_account_id
#: model:ir.model.fields,field_description:account.field_account_register_payments__writeoff_account_id
msgid "Difference Account"
msgstr "Konto razlike"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__difference
msgid ""
"Difference between the computed ending balance and the specified ending "
"balance."
msgstr ""

#. module: account
#: model:ir.model,name:account.model_digest_digest
msgid "Digest"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_config_settings__group_products_in_bills
msgid ""
"Disable this option to use a simplified versions of vendor bills, where "
"products are hidden."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Disable to have a simplified view of vendor bills, without the products."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_form
msgid "Disc (%)"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line__discount
msgid "Discount (%)"
msgstr "Popust (%)"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment__display_name
#: model:ir.model.fields,field_description:account.field_account_account__display_name
#: model:ir.model.fields,field_description:account.field_account_account_tag__display_name
#: model:ir.model.fields,field_description:account.field_account_account_template__display_name
#: model:ir.model.fields,field_description:account.field_account_account_type__display_name
#: model:ir.model.fields,field_description:account.field_account_bank_statement__display_name
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox__display_name
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance__display_name
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__display_name
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__display_name
#: model:ir.model.fields,field_description:account.field_account_cashbox_line__display_name
#: model:ir.model.fields,field_description:account.field_account_chart_template__display_name
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__display_name
#: model:ir.model.fields,field_description:account.field_account_common_report__display_name
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account__display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template__display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax__display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template__display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_year__display_name
#: model:ir.model.fields,field_description:account.field_account_full_reconcile__display_name
#: model:ir.model.fields,field_description:account.field_account_group__display_name
#: model:ir.model.fields,field_description:account.field_account_incoterms__display_name
#: model:ir.model.fields,field_description:account.field_account_invoice__display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm__display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_import_wizard__display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_line__display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_refund__display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_report__display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_send__display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_tax__display_name
#: model:ir.model.fields,field_description:account.field_account_journal__display_name
#: model:ir.model.fields,field_description:account.field_account_move__display_name
#: model:ir.model.fields,field_description:account.field_account_move_line__display_name
#: model:ir.model.fields,field_description:account.field_account_move_reversal__display_name
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__display_name
#: model:ir.model.fields,field_description:account.field_account_payment__display_name
#: model:ir.model.fields,field_description:account.field_account_payment_method__display_name
#: model:ir.model.fields,field_description:account.field_account_payment_term__display_name
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__display_name
#: model:ir.model.fields,field_description:account.field_account_print_journal__display_name
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__display_name
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__display_name
#: model:ir.model.fields,field_description:account.field_account_reconciliation_widget__display_name
#: model:ir.model.fields,field_description:account.field_account_register_payments__display_name
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__display_name
#: model:ir.model.fields,field_description:account.field_account_tax__display_name
#: model:ir.model.fields,field_description:account.field_account_tax_group__display_name
#: model:ir.model.fields,field_description:account.field_account_tax_template__display_name
#: model:ir.model.fields,field_description:account.field_account_unreconcile__display_name
#: model:ir.model.fields,field_description:account.field_cash_box_in__display_name
#: model:ir.model.fields,field_description:account.field_cash_box_out__display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_agedpartnerbalance__display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_invoice_with_payments__display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_journal__display_name
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__display_name
#: model:ir.model.fields,field_description:account.field_validate_account_move__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__qr_code
#: model:ir.model.fields,field_description:account.field_res_config_settings__qr_code
msgid "Display SEPA QR code"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line__display_type
msgid "Display Type"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_template__description
msgid "Display on Invoices"
msgstr "Prikaži na fakturama"

#. module: account
#: code:addons/account/models/digest.py:16
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__auto_delete_message
msgid ""
"Do not keep a copy of the email in the document communication history (mass "
"mailing only)"
msgstr ""
"Ne čuvaj kopiju email poruke u istoriji komunikacije dokumenta (samo masovna"
" epošta)"

#. module: account
#: selection:res.company,account_dashboard_onboarding_state:0
#: selection:res.company,account_invoice_onboarding_state:0
#: selection:res.company,account_onboarding_invoice_layout_state:0
#: selection:res.company,account_onboarding_sale_tax_state:0
#: selection:res.company,account_onboarding_sample_invoice_state:0
#: selection:res.company,account_setup_bank_data_state:0
#: selection:res.company,account_setup_coa_state:0
#: selection:res.company,account_setup_fy_data_state:0
msgid "Done"
msgstr "Gotovo"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_invoice_page
msgid "Download"
msgstr "Preuzimanje"

#. module: account
#: selection:account.invoice,state:0 selection:account.invoice.report,state:0
#: selection:account.payment,state:0
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_search
msgid "Draft"
msgstr "U pripremi"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_form
msgid "Draft Credit Note"
msgstr "Knjižno odobrenje u pripremi"

#. module: account
#: code:addons/account/models/account_invoice.py:484
#: model_terms:ir.ui.view,arch_db:account.invoice_form
#: model_terms:ir.ui.view,arch_db:account.portal_my_invoices
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
#, python-format
msgid "Draft Invoice"
msgstr "Faktura u pripremi"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Draft Invoices"
msgstr "Fakture u pripremi"

#. module: account
#: model:ir.actions.act_window,name:account.action_bank_statement_draft_tree
msgid "Draft statements"
msgstr "Izvodi u pripremi"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_line_form
msgid "Due"
msgstr "Krajnji rok"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__residual
msgid "Due Amount"
msgstr "Iznos van valute"

#. module: account
#. openerp-web
#: code:addons/account/controllers/portal.py:41
#: code:addons/account/static/src/xml/account_reconciliation.xml:288
#: model:ir.model.fields,field_description:account.field_account_invoice__date_due
#: model:ir.model.fields,field_description:account.field_account_invoice_report__date_due
#: model_terms:ir.ui.view,arch_db:account.portal_my_invoices
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
#, python-format
msgid "Due Date"
msgstr "Datum dospijeća"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_line_form
msgid "Due Date Computation"
msgstr "Izračun datuma dospjeća"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_line_tree
msgid "Due Type"
msgstr "Tip prekoračenja"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__date_maturity
msgid "Due date"
msgstr "Datum dospijeća"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_line_form
msgid "Due the"
msgstr ""

#. module: account
#: code:addons/account/models/account_invoice.py:1271
#, python-format
msgid ""
"Duplicated vendor reference detected. You probably encoded twice the same "
"vendor bill/credit note."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_reports
msgid "Dynamic Reports"
msgstr "Dinamični izvještaji"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_l10n_eu_service
msgid "EU Digital Goods VAT"
msgstr ""

#. module: account
#: model:account.incoterms,name:account.incoterm_EXW
msgid "EX WORKS"
msgstr ""

#. module: account
#: code:addons/account/models/chart_template.py:387
#: code:addons/account/models/chart_template.py:402
#: code:addons/account/models/chart_template.py:412
#, python-format
msgid "EXCH"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:973
#, python-format
msgid "Either pass both debit and credit or none."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__is_email
msgid "Email"
msgstr "E-Mail"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Email Alias"
msgstr "Email nadimak"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__email_from
msgid ""
"Email address of the sender. This field is set when no matching partner is "
"found and replaces the author_id field in the chatter."
msgstr ""
"Email adresa pošaljioca. Ovo polje je postavljeno kada nema odgovarajućeg "
"partnera i zamjenjuje author_id polje i u chateru."

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__invoice_is_email
msgid "Email by default"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Email your Vendor Bills"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__date_to
#: model:ir.model.fields,field_description:account.field_account_common_report__date_to
#: model:ir.model.fields,field_description:account.field_account_fiscal_year__date_to
#: model:ir.model.fields,field_description:account.field_account_print_journal__date_to
msgid "End Date"
msgstr "Datum Završetka"

#. module: account
#: model:account.payment.term,name:account.account_payment_term
msgid "End of Following Month"
msgstr "Kraj trenutnog mjeseca"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__balance_end_real
msgid "Ending Balance"
msgstr "Završni saldo"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__cashbox_end_id
msgid "Ending Cashbox"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_year__date_to
msgid "Ending Date, included in the fiscal year."
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_move_line_form
msgid "Entries"
msgstr "Unosi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_print_journal__sort_selection
msgid "Entries Sorted by"
msgstr "Sortirano po"

#. module: account
#: code:addons/account/models/account_move.py:911
#, python-format
msgid "Entries are not from the same account."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Entries to Review"
msgstr "Zapisi za provjeriti"

#. module: account
#: code:addons/account/models/account_analytic_line.py:47
#, python-format
msgid "Entries: "
msgstr "Zapisi: "

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__sequence_id
msgid "Entry Sequence"
msgstr "Redoslijed unosa"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__move_line_ids
msgid "Entry lines"
msgstr "Stavke unosa"

#. module: account
#: selection:account.account.type,internal_group:0
#: model:account.account.type,name:account.data_account_type_equity
#: model_terms:ir.ui.view,arch_db:account.view_account_form
#: model_terms:ir.ui.view,arch_db:account.view_account_search
msgid "Equity"
msgstr "Dionica"

#. module: account
#: code:addons/account/models/res_config_settings.py:145
#, python-format
msgid "Error!"
msgstr "Greška !"

#. module: account
#: code:addons/account/models/chart_template.py:412
#, python-format
msgid "Exchange Difference"
msgstr "Kursna razlika"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__currency_exchange_journal_id
#: model:ir.model.fields,field_description:account.field_res_config_settings__currency_exchange_journal_id
msgid "Exchange Gain or Loss Journal"
msgstr "Konto dobitka/gubitka kursne razlike"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_full_reconcile__exchange_move_id
msgid "Exchange Move"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__expects_chart_of_accounts
msgid "Expects a Chart of Accounts"
msgstr "Očekuje kontni plan"

#. module: account
#: selection:account.account.type,internal_group:0
msgid "Expense"
msgstr "Trošak"

#. module: account
#: model:ir.model.fields,field_description:account.field_product_category__property_account_expense_categ_id
#: model:ir.model.fields,field_description:account.field_product_template__property_account_expense_id
#: model_terms:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
msgid "Expense Account"
msgstr "Konto za rashode"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__property_account_expense_id
msgid "Expense Account on Product Template"
msgstr "Račun troškova na predlošku proizvoda"

#. module: account
#: model:account.account.type,name:account.data_account_type_expenses
#: model_terms:ir.ui.view,arch_db:account.view_account_form
#: model_terms:ir.ui.view,arch_db:account.view_account_search
msgid "Expenses"
msgstr "Troškovi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__reference
msgid "External Reference"
msgstr "Eksterna referenca"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:318
#: code:addons/account/static/src/xml/account_reconciliation.xml:333
#, python-format
msgid "External link"
msgstr ""

#. module: account
#: model:account.incoterms,name:account.incoterm_FAS
msgid "FREE ALONGSIDE SHIP"
msgstr ""

#. module: account
#: model:account.incoterms,name:account.incoterm_FCA
msgid "FREE CARRIER"
msgstr ""

#. module: account
#: model:account.incoterms,name:account.incoterm_FOB
msgid "FREE ON BOARD"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__starred_partner_ids
msgid "Favorited By"
msgstr "Odabrao "

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_search
msgid "Favorites"
msgstr "Favoriti"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "February"
msgstr "Februar"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__state_ids
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__state_ids
msgid "Federal States"
msgstr "Federalne jedinice"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_import_wizard__attachment_ids
msgid "Files"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.cash_box_in_form
msgid "Fill in this form if you put money in the cash register:"
msgstr "Ispunite ovaj obrazac za polaganje novca u blagajnu:"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:147
#, python-format
msgid "Filter..."
msgstr "Filter..."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_analytic_line__general_account_id
#: model_terms:ir.ui.view,arch_db:account.view_account_analytic_line_filter_inherit_account
msgid "Financial Account"
msgstr "Finansijski konto"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Fiscal Information"
msgstr "Fiskalne informacije"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Fiscal Localization"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template__position_id
msgid "Fiscal Mapping"
msgstr "Fiskalna pozicija"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Fiscal Periods"
msgstr "Fiskalni periodi"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_fiscal_position_template_form
#: model:ir.model,name:account.model_account_fiscal_position
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account__position_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax__position_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template__position_id
#: model:ir.model.fields,field_description:account.field_account_invoice__fiscal_position_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report__fiscal_position_id
#: model:ir.model.fields,field_description:account.field_res_partner__property_account_position_id
#: model_terms:ir.ui.view,arch_db:account.view_account_position_form
#: model_terms:ir.ui.view,arch_db:account.view_account_position_template_search
#: model_terms:ir.ui.view,arch_db:account.view_account_position_template_tree
#: model_terms:ir.ui.view,arch_db:account.view_account_position_tree
msgid "Fiscal Position"
msgstr "Fiskalna pozicija"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__name
#: model_terms:ir.ui.view,arch_db:account.view_account_position_template_form
#: model_terms:ir.ui.view,arch_db:account.view_account_position_template_search
msgid "Fiscal Position Template"
msgstr "Predložak fiskalne pozicije"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_fiscal_position_form
#: model:ir.ui.menu,name:account.menu_action_account_fiscal_position_form
msgid "Fiscal Positions"
msgstr "Fiskalne pozicije"

#. module: account
#: code:addons/account/models/company.py:293
#: model:ir.model,name:account.model_account_fiscal_year
#: model_terms:ir.ui.view,arch_db:account.onboarding_fiscal_year_step
#, python-format
msgid "Fiscal Year"
msgstr "Fisklana godina"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.action_account_fiscal_year_form
msgid "Fiscal Year 2018"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.actions_account_fiscal_year
#: model:ir.model.fields,field_description:account.field_res_config_settings__group_fiscal_year
msgid "Fiscal Years"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__fiscalyear_last_day
#: model:ir.model.fields,field_description:account.field_res_company__fiscalyear_last_day
msgid "Fiscalyear Last Day"
msgstr "Zadnji datum fiskalne godine"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__fiscalyear_last_month
#: model:ir.model.fields,field_description:account.field_res_company__fiscalyear_last_month
msgid "Fiscalyear Last Month"
msgstr "Zadnji mjesec fiskalne godine"

#. module: account
#: selection:account.reconcile.model,amount_type:0
#: selection:account.reconcile.model,second_amount_type:0
#: selection:account.reconcile.model.template,amount_type:0
#: selection:account.reconcile.model.template,second_amount_type:0
#: selection:account.tax,amount_type:0
#: selection:account.tax.template,amount_type:0
msgid "Fixed"
msgstr "Fiksno"

#. module: account
#: selection:account.payment.term.line,value:0
msgid "Fixed Amount"
msgstr "Fiksni iznos"

#. module: account
#: model:account.account.type,name:account.data_account_type_fixed_assets
msgid "Fixed Assets"
msgstr "Osnovna sredstva"

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__amount
#: model:ir.model.fields,help:account.field_account_reconcile_model__second_amount
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__amount
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__second_amount
msgid ""
"Fixed amount will count as a debit if it is negative, as a credit if it is "
"positive."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_reports_followup
msgid "Follow-up Levels"
msgstr "Nivoi praćenja"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_follower_ids
#: model:ir.model.fields,field_description:account.field_account_invoice__message_follower_ids
#: model:ir.model.fields,field_description:account.field_account_payment__message_follower_ids
msgid "Followers"
msgstr "Pratioci"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_channel_ids
#: model:ir.model.fields,field_description:account.field_account_invoice__message_channel_ids
#: model:ir.model.fields,field_description:account.field_account_payment__message_channel_ids
msgid "Followers (Channels)"
msgstr "Pratioci (Kanali)"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_partner_ids
#: model:ir.model.fields,field_description:account.field_account_invoice__message_partner_ids
#: model:ir.model.fields,field_description:account.field_account_payment__message_partner_ids
msgid "Followers (Partners)"
msgstr "Pratioci (Partneri)"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__inbound_payment_method_ids
msgid "For Incoming Payments"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__outbound_payment_method_ids
msgid "For Outgoing Payments"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_term_line__value_amount
msgid "For percent enter a ratio between 0-100."
msgstr ""

#. module: account
#: sql_constraint:account.invoice.line:0
msgid ""
"Forbidden unit price, account and quantity on non-accountable invoice line"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__force_second_tax_included
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__force_second_tax_included
msgid "Force the second tax to be managed as a price included tax."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__force_tax_included
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__force_tax_included
msgid "Force the tax to be managed as a price included tax."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_account__currency_id
msgid "Forces all moves for this account to have this account currency."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_account_template__currency_id
msgid "Forces all moves for this account to have this secondary currency."
msgstr "Forsira da sva knjiženja ovog konta moraju imati sekundarnu valutu."

#. module: account
#: code:addons/account/report/account_aged_partner_balance.py:218
#: code:addons/account/report/account_journal.py:101
#, python-format
msgid "Form content is missing, this report cannot be printed."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__email_from
msgid "From"
msgstr "Od"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "From Payable accounts"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "From Receivable accounts"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_invoice_report_all_supp
msgid ""
"From this report, you can have an overview of the amount invoiced from your "
"vendors. The search tool can also be used to personalise your Invoices "
"reports and so, match this analysis to your needs."
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_invoice_report_all
msgid ""
"From this report, you can have an overview of the amount invoiced to your "
"customers. The search tool can also be used to personalise your Invoices "
"reports and so, match this analysis to your needs."
msgstr ""

#. module: account
#: code:addons/account/models/account_invoice.py:385
#, python-format
msgid "From: "
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_full_reconcile
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__full_reconcile_id
msgid "Full Reconcile"
msgstr "Kompletno zatvaranje"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:102
#, python-format
msgid "Future"
msgstr "Buduće"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Future Activities"
msgstr "Buduće aktivnosti"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_form
msgid "GROSS PROFIT"
msgstr "UKUPAN PROFIT"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__income_currency_exchange_account_id
#: model:ir.model.fields,field_description:account.field_res_company__income_currency_exchange_account_id
msgid "Gain Exchange Rate Account"
msgstr "Konto pozitivne kursne razlike"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_entries_generate_entries
msgid "Generate Entries"
msgstr "Generiši stavke"

#. module: account
#: model:ir.ui.menu,name:account.account_reports_legal_statements_menu
msgid "Generic Statements"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Get warnings when invoicing specific customers"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Get your bank statements automatically imported every 4 hours, or in one-"
"click, using Yodlee and Plaid services. Once installed, set “Bank Feeds” to "
"“Bank Synchronization” in bank account settings. Then, click “Configure” on "
"the online account to enter your bank credentials."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line__sequence
msgid "Gives the sequence of this line when displaying the invoice."
msgstr "Daje sekvencu ove linije kada se prikazuje faktura"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line__sequence
msgid ""
"Gives the sequence order when displaying a list of bank statement lines."
msgstr "Daje redoslijed kod prikaza liste stavaka izvoda."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_tax__sequence
msgid "Gives the sequence order when displaying a list of invoice tax."
msgstr "Određuje redosljed sekvenci kada prikazuje listu poreza fakture."

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_term_line__sequence
msgid ""
"Gives the sequence order when displaying a list of payment terms lines."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:63
#, python-format
msgid "Go to bank statement(s)"
msgstr ""

#. module: account
#: code:addons/account/models/account_invoice.py:758
#: code:addons/account/models/company.py:521
#, python-format
msgid "Go to the configuration panel"
msgstr "Idite na panel konfiguracije"

#. module: account
#: code:addons/account/models/company.py:459
#, python-format
msgid "Go to the journal configuration"
msgstr ""

#. module: account
#: selection:res.partner,trust:0
msgid "Good Debtor"
msgstr "Dobar platioc"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:48
#, python-format
msgid "Good Job!"
msgstr "Dobar posao!"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__group_id
#: model:ir.model.fields,field_description:account.field_account_account_template__group_id
msgid "Group"
msgstr "Grupa"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_search
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_search
#: model_terms:ir.ui.view,arch_db:account.view_account_template_search
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_search
msgid "Group By"
msgstr "Grupiši po"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__group_invoice_lines
msgid "Group Invoice Lines"
msgstr "Grupiši stavke fakture"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_register_payments__group_invoices
msgid "Group Invoices"
msgstr ""

#. module: account
#: selection:account.tax,amount_type:0
#: selection:account.tax.template,amount_type:0
msgid "Group of Taxes"
msgstr "Grupa poreza"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Group payments into a single batch to ease the reconciliation process"
msgstr ""

#. module: account
#: selection:account.cash.rounding,rounding_method:0
msgid "HALF-UP"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__has_accounting_entries
msgid "Has Accounting Entries"
msgstr "Ima zapisa knjiženja"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__has_invoices
msgid "Has Invoices"
msgstr "Ima faktura"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__has_outstanding
msgid "Has Outstanding"
msgstr "Ima van valute"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__has_unreconciled_entries
msgid "Has Unreconciled Entries"
msgstr "Ima nezatvorenih zapisa"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__qr_code_valid
msgid "Has all required arguments"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__has_error
#: model:ir.model.fields,help:account.field_account_invoice_send__has_error
msgid "Has error"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment__hide_payment_method
#: model:ir.model.fields,field_description:account.field_account_payment__hide_payment_method
#: model:ir.model.fields,field_description:account.field_account_register_payments__hide_payment_method
msgid "Hide Payment Method"
msgstr "Sakrij način plaćanja"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__hide_tax_exigibility
msgid "Hide Use Cash Basis Option"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_invoice_chatter
msgid "History"
msgstr "Istorija"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_form
msgid "How do account types affect your reports?"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "How total tax amount is computed in orders and invoices"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment__id
#: model:ir.model.fields,field_description:account.field_account_account__id
#: model:ir.model.fields,field_description:account.field_account_account_tag__id
#: model:ir.model.fields,field_description:account.field_account_account_template__id
#: model:ir.model.fields,field_description:account.field_account_account_type__id
#: model:ir.model.fields,field_description:account.field_account_bank_statement__id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox__id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance__id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__id
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__id
#: model:ir.model.fields,field_description:account.field_account_cashbox_line__id
#: model:ir.model.fields,field_description:account.field_account_chart_template__id
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__id
#: model:ir.model.fields,field_description:account.field_account_common_report__id
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account__id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template__id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax__id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template__id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__id
#: model:ir.model.fields,field_description:account.field_account_fiscal_year__id
#: model:ir.model.fields,field_description:account.field_account_full_reconcile__id
#: model:ir.model.fields,field_description:account.field_account_group__id
#: model:ir.model.fields,field_description:account.field_account_incoterms__id
#: model:ir.model.fields,field_description:account.field_account_invoice__id
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm__id
#: model:ir.model.fields,field_description:account.field_account_invoice_import_wizard__id
#: model:ir.model.fields,field_description:account.field_account_invoice_line__id
#: model:ir.model.fields,field_description:account.field_account_invoice_refund__id
#: model:ir.model.fields,field_description:account.field_account_invoice_report__id
#: model:ir.model.fields,field_description:account.field_account_invoice_send__id
#: model:ir.model.fields,field_description:account.field_account_invoice_tax__id
#: model:ir.model.fields,field_description:account.field_account_journal__id
#: model:ir.model.fields,field_description:account.field_account_move__id
#: model:ir.model.fields,field_description:account.field_account_move_line__id
#: model:ir.model.fields,field_description:account.field_account_move_reversal__id
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__id
#: model:ir.model.fields,field_description:account.field_account_payment__id
#: model:ir.model.fields,field_description:account.field_account_payment_method__id
#: model:ir.model.fields,field_description:account.field_account_payment_term__id
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__id
#: model:ir.model.fields,field_description:account.field_account_print_journal__id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__id
#: model:ir.model.fields,field_description:account.field_account_reconciliation_widget__id
#: model:ir.model.fields,field_description:account.field_account_register_payments__id
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__id
#: model:ir.model.fields,field_description:account.field_account_tax__id
#: model:ir.model.fields,field_description:account.field_account_tax_group__id
#: model:ir.model.fields,field_description:account.field_account_tax_template__id
#: model:ir.model.fields,field_description:account.field_account_unreconcile__id
#: model:ir.model.fields,field_description:account.field_cash_box_in__id
#: model:ir.model.fields,field_description:account.field_cash_box_out__id
#: model:ir.model.fields,field_description:account.field_report_account_report_agedpartnerbalance__id
#: model:ir.model.fields,field_description:account.field_report_account_report_invoice_with_payments__id
#: model:ir.model.fields,field_description:account.field_report_account_report_journal__id
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__id
#: model:ir.model.fields,field_description:account.field_validate_account_move__id
msgid "ID"
msgstr "ID"

#. module: account
#: code:addons/account/models/chart_template.py:409
#, python-format
msgid "INV"
msgstr "FAK"

#. module: account
#: code:addons/account/models/account_bank_statement.py:325
#, python-format
msgid "If \"Amount Currency\" is specified, then \"Amount\" must be as well."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__message_unread
#: model:ir.model.fields,help:account.field_account_invoice__message_unread
#: model:ir.model.fields,help:account.field_account_payment__message_unread
msgid "If checked new messages require your attention."
msgstr "Ako je označeno nove poruke će zahtjevati vašu pažnju."

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__message_needaction
#: model:ir.model.fields,help:account.field_account_invoice__message_needaction
#: model:ir.model.fields,help:account.field_account_payment__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Ako je zakačeno, nove poruke će zahtjevati vašu pažnju"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__message_has_error
#: model:ir.model.fields,help:account.field_account_invoice__message_has_error
#: model:ir.model.fields,help:account.field_account_payment__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_account_template__nocreate
msgid ""
"If checked, the new chart of accounts will not contain this by default."
msgstr "Ako je označeno, novi kontni plan zadano neće sadržavati."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_reversal__journal_id
msgid "If empty, uses the journal of the journal entry to be reversed."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_register_payments__group_invoices
msgid ""
"If enabled, groups invoices by commercial partner, invoice account,\n"
"                                                                    type and recipient bank account in the generated payments. If disabled,\n"
"                                                                    a distinct payment will be generated for each invoice."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__include_base_amount
#: model:ir.model.fields,help:account.field_account_tax_template__include_base_amount
msgid ""
"If set, taxes which are computed after this one will be computed based on "
"the price tax included."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__accounting_date
msgid ""
"If set, the accounting entries created during the bank statement reconciliation process will be created at this date.\n"
"This is useful if the accounting period in which the entries should normally be booked is already closed."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__analytic
#: model:ir.model.fields,help:account.field_account_tax_template__analytic
msgid ""
"If set, the amount computed by this tax will be assigned to the same "
"analytic account as the invoice line (if any)"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_term__active
msgid ""
"If the active field is set to False, it will allow you to hide the payment "
"terms without removing it."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__group_invoice_lines
msgid ""
"If this box is checked, the system will try to group the accounting lines "
"when generating them from invoices."
msgstr ""
"Ukoliko je ovo polje označeno sistem će pokušati da grupira unose knjiženja "
"kada ih generira iz faktura."

#. module: account
#: model:ir.model.fields,help:account.field_account_move__auto_reverse
msgid ""
"If this checkbox is ticked, this entry will be automatically reversed at the"
" reversal date you defined."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"If you check this box, you will be able to collect payments using SEPA "
"Direct Debit mandates."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"If you check this box, you will be able to register your payment using SEPA."
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.open_account_journal_dashboard_kanban
msgid ""
"If you have not installed a chart of account, please install one first.<br>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_unreconcile_view
msgid ""
"If you unreconcile transactions, you must also verify all the actions that "
"are linked to those transactions because they will not be disabled"
msgstr ""
"Ako razvežete transakcije, morate također verifikovati sve akcije povezane "
"sa tim transakcijama jer one neće biti onemogućene."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__payment_term_id
msgid ""
"If you use payment terms, the due date will be computed automatically at the"
" generation of accounting entries. If you keep the payment terms and the due"
" date empty, it means direct payment. The payment terms may compute several "
"due dates, for example 50% now, 50% in one month."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__date_due
msgid ""
"If you use payment terms, the due date will be computed automatically at the"
" generation of accounting entries. The Payment terms may compute several due"
" dates, for example 50% now and 50% in one month, but if you want to force a"
" due date, make sure that the payment term is not set on the invoice. If you"
" keep the Payment terms and the due date empty, it means direct payment."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"If you're selling digital goods to customers in the EU, you must charge VAT "
"based on your customers' locations. This rule applies regardless of you are "
"located. Digital goods are defined in the legislation as broadcasting, "
"telecommunications, and services that are electronically supplied instead of"
" shipped. Gift cards sent online are not included in the definition."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line__product_image
msgid ""
"Image of the product variant (Big-sized image of product template if false)."
" It is automatically resized as a 1024x1024px image, with aspect ratio "
"preserved."
msgstr ""

#. module: account
#: model:account.payment.term,name:account.account_payment_term_immediate
msgid "Immediate Payment"
msgstr "Neposredno plaćanje"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_import_wizard_form_view
msgid "Import"
msgstr "Uvoz"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_bank_statement_import_qif
msgid "Import .qif files"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.account_invoice_import_wizard_action
msgid "Import Vendor Bills"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_invoice_import_wizard
msgid "Import Your Vendor Bills from Files."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_bank_statement_import_csv
msgid "Import in .csv format"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_bank_statement_import_ofx
msgid "Import in .ofx format"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_bank_statement_import_camt
msgid "Import in CAMT.053 format"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Import your bank statements automatically"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Import your bank statements in CAMT.053"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Import your bank statements in CSV"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Import your bank statements in OFX"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Import your bank statements in QIF"
msgstr ""

#. module: account
#: selection:account.invoice,state:0
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "In Payment"
msgstr ""

#. module: account
#: code:addons/account/models/account_bank_statement.py:345
#, python-format
msgid ""
"In order to delete a bank statement line, you must first cancel it to delete"
" related journal items."
msgstr ""

#. module: account
#: code:addons/account/models/account_bank_statement.py:201
#, python-format
msgid ""
"In order to delete a bank statement, you must first cancel it to delete "
"related journal items."
msgstr ""
"Kako bi izbrisali bankovni izvod, morate ga prvo otkazati kako bi se "
"obrisale sve stavke dnevnika povezane s njim."

#. module: account
#: code:addons/account/models/account_payment.py:89
#, python-format
msgid ""
"In order to pay multiple invoices at once, they must use the same currency."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Inactive"
msgstr "Neaktivan"

#. module: account
#: selection:account.payment.method,payment_type:0
msgid "Inbound"
msgstr "Dolazni"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__analytic
msgid "Include in Analytic Cost"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__price_include
#: model:ir.model.fields,field_description:account.field_account_tax_template__price_include
msgid "Included in Price"
msgstr "Uključeno u cijenu"

#. module: account
#: selection:account.account.type,internal_group:0
#: model:account.account.type,name:account.data_account_type_revenue
#: model_terms:ir.ui.view,arch_db:account.view_account_form
#: model_terms:ir.ui.view,arch_db:account.view_account_search
msgid "Income"
msgstr "Prihod"

#. module: account
#: model:ir.model.fields,field_description:account.field_product_category__property_account_income_categ_id
#: model:ir.model.fields,field_description:account.field_product_template__property_account_income_id
#: model_terms:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
msgid "Income Account"
msgstr "Konto prihoda"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__property_account_income_id
msgid "Income Account on Product Template"
msgstr "Konto prihoda na predlošku proizvoda"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_model.js:740
#, python-format
msgid "Incorrect Operation"
msgstr "Nepravilna operacija"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__incoterm_id
msgid "Incoterm"
msgstr "Incoterm"

#. module: account
#: model:ir.model.fields,help:account.field_account_incoterms__code
msgid "Incoterm Standard Code"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_incoterms_tree
#: model:ir.model,name:account.model_account_incoterms
#: model:ir.ui.menu,name:account.menu_action_incoterm_open
#: model_terms:ir.ui.view,arch_db:account.account_incoterms_form
#: model_terms:ir.ui.view,arch_db:account.account_incoterms_view_search
#: model_terms:ir.ui.view,arch_db:account.view_incoterms_tree
msgid "Incoterms"
msgstr "Incoterms"

#. module: account
#: model:ir.model.fields,help:account.field_account_incoterms__name
msgid ""
"Incoterms are series of sales terms. They are used to divide transaction "
"costs and responsibilities between buyer and seller and reflect state-of-"
"the-art transportation practices."
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_incoterms_tree
msgid ""
"Incoterms are used to divide transaction costs and responsibilities between "
"buyer and seller."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:25
#, python-format
msgid "Info"
msgstr "Informacija"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
msgid "Information"
msgstr "Informacije"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__parent_id
msgid "Initial thread message."
msgstr "Inicijalna nit poruke"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__property_stock_account_input_categ_id
#: model:ir.model.fields,field_description:account.field_res_company__property_stock_account_input_categ_id
msgid "Input Account for Stock Valuation"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Install More Packages"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__transfer_account_id
msgid "Inter-Banks Transfer Account"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_company__transfer_account_id
msgid ""
"Intermediary account used when moving money from a liquidity account to "
"another"
msgstr ""
"Prenosni konto korišćen prilikom prenosa novca sa jednog konta likvidnosti "
"na drugi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__internal_group
#: model:ir.model.fields,field_description:account.field_account_account_type__internal_group
msgid "Internal Group"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__narration
msgid "Internal Note"
msgstr "Interna bilješka"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__note
msgid "Internal Notes"
msgstr "Interne zabilješke"

#. module: account
#: selection:account.payment,payment_type:0
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Internal Transfer"
msgstr "Interni prenos"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_payments_transfer
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Internal Transfers"
msgstr "Interni prenosi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__internal_type
msgid "Internal Type"
msgstr "Interni tip"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_template_form
msgid "Internal notes..."
msgstr "Interna bilješka ..."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__incoterm_id
#: model:ir.model.fields,help:account.field_res_company__incoterm_id
msgid ""
"International Commercial Terms are a series of predefined commercial terms "
"used in international transactions."
msgstr ""
"Internacionalni komercijalni uslovi su niz predefiniranih komercijalnih "
"uslova koji se koriste za inostrane transakcije."

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_intrastat
msgid "Intrastat"
msgstr ""

#. module: account
#: code:addons/account/models/partner.py:45
#, python-format
msgid "Invalid \"Zip Range\", please configure it properly."
msgstr ""

#. module: account
#: code:addons/account/models/account_invoice.py:1314
#: model:ir.model,name:account.model_account_invoice
#: model:ir.model.fields,field_description:account.field_account_invoice_report__invoice_id
#: model:ir.model.fields,field_description:account.field_account_invoice_tax__invoice_id
#: model:ir.model.fields,field_description:account.field_account_move_line__invoice_id
#: model:ir.model.fields,field_description:account.field_res_partner__invoice_warn
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.invoice_form
#: model_terms:ir.ui.view,arch_db:account.invoice_tree
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#, python-format
msgid "Invoice"
msgstr "Faktura"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__number
#: model_terms:ir.ui.view,arch_db:account.portal_my_invoices
msgid "Invoice #"
msgstr "Faktura #"

#. module: account
#: code:addons/account/models/account_invoice.py:485
#, python-format
msgid "Invoice - %s"
msgstr "Faktura - %s"

#. module: account
#: model:mail.message.subtype,description:account.mt_invoice_created
#: model:mail.message.subtype,name:account.mt_invoice_created
msgid "Invoice Created"
msgstr "Faktura kreirana"

#. module: account
#: code:addons/account/controllers/portal.py:40
#: model:ir.model.fields,field_description:account.field_account_invoice__date_invoice
#: model:ir.model.fields,field_description:account.field_account_invoice_report__date
#: model_terms:ir.ui.view,arch_db:account.portal_my_invoices
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#, python-format
msgid "Invoice Date"
msgstr "Datum Fakture"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__invoice_icon
msgid "Invoice Icon"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_invoice_layout_step
msgid "Invoice Layout"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_invoice_line
#: model_terms:ir.ui.view,arch_db:account.view_invoice_line_tree
msgid "Invoice Line"
msgstr "Stavka fakture"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__invoice_line_ids
#: model_terms:ir.ui.view,arch_db:account.invoice_form
msgid "Invoice Lines"
msgstr "Stavke fakture"

#. module: account
#: sql_constraint:account.invoice:0
msgid "Invoice Number must be unique per Company!"
msgstr "Broj fakture se ne smije ponavljati za jednu organizaciju."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_form
msgid "Invoice Number:"
msgstr "Broj fakture:"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_payment
msgid "Invoice Online Payment"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line__invoice_id
msgid "Invoice Reference"
msgstr "Referenca Fakture"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__state
msgid "Invoice Status"
msgstr "Status fakture"

#. module: account
#: model:ir.model,name:account.model_account_invoice_tax
msgid "Invoice Tax"
msgstr "Porez fakture"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__refund_invoice_id
msgid "Invoice for which this invoice is the credit note"
msgstr ""

#. module: account
#: code:addons/account/models/account_invoice.py:869
#, python-format
msgid "Invoice must be cancelled in order to reset it to draft."
msgstr ""

#. module: account
#: code:addons/account/models/account_invoice.py:893
#, python-format
msgid "Invoice must be in draft state in order to validate it."
msgstr ""

#. module: account
#: code:addons/account/models/account_invoice.py:920
#, python-format
msgid "Invoice must be paid in order to set it to register payment."
msgstr ""

#. module: account
#: code:addons/account/models/account_invoice.py:907
#, python-format
msgid "Invoice must be validated in order to set it to register payment."
msgstr ""

#. module: account
#: model:mail.message.subtype,description:account.mt_invoice_paid
msgid "Invoice paid"
msgstr "Faktura plaćena"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
msgid "Invoice send & Print"
msgstr ""

#. module: account
#: model:mail.message.subtype,description:account.mt_invoice_validated
msgid "Invoice validated"
msgstr "Faktura odobrena"

#. module: account
#: model:mail.template,report_name:account.email_template_edi_invoice
msgid ""
"Invoice_${(object.number or '').replace('/','_')}${object.state == 'draft' "
"and '_draft' or ''}"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Invoiced"
msgstr "Fakturisano"

#. module: account
#: code:addons/account/models/account_invoice_import_wizard.py:38
#: model:ir.actions.act_window,name:account.action_invoice_refund_out_tree
#: model:ir.actions.act_window,name:account.action_invoice_tree
#: model:ir.actions.act_window,name:account.action_invoice_tree1
#: model:ir.actions.report,name:account.account_invoices
#: model:ir.model.fields,field_description:account.field_account_abstract_payment__invoice_ids
#: model:ir.model.fields,field_description:account.field_account_invoice_send__invoice_ids
#: model:ir.model.fields,field_description:account.field_account_payment__invoice_ids
#: model:ir.model.fields,field_description:account.field_account_register_payments__invoice_ids
#: model:ir.model.fields,field_description:account.field_res_partner__invoice_ids
#: model:ir.ui.menu,name:account.menu_action_account_invoice_report_all
#: model:ir.ui.menu,name:account.menu_action_invoice_tree1
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.portal_my_home_invoice
#: model_terms:ir.ui.view,arch_db:account.portal_my_home_menu_invoice
#: model_terms:ir.ui.view,arch_db:account.portal_my_invoices
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_form
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_graph
#: model_terms:ir.ui.view,arch_db:account.view_invoice_graph
#: model_terms:ir.ui.view,arch_db:account.view_invoice_line_calendar
#: model_terms:ir.ui.view,arch_db:account.view_invoice_pivot
#, python-format
msgid "Invoices"
msgstr "Fakture"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_invoice_report_all
#: model:ir.actions.act_window,name:account.action_account_invoice_report_all_supp
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_graph
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_pivot
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Invoices Analysis"
msgstr "Analiza faktura"

#. module: account
#: model:ir.model,name:account.model_account_invoice_report
msgid "Invoices Statistics"
msgstr "Statistika faktura"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:178
#, python-format
msgid "Invoices owed to you"
msgstr "Fakture koje duguju vama"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Invoices to Validate"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_payment__reconciled_invoice_ids
msgid "Invoices whose journal items have been reconciled with this payment's."
msgstr ""

#. module: account
#: model:ir.actions.report,name:account.account_invoices_without_payment
msgid "Invoices without Payment"
msgstr "Fakture bez uplata"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance
#: model_terms:ir.ui.view,arch_db:account.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Invoicing"
msgstr "Fakturisanje"

#. module: account
#: selection:account.reconcile.model,match_amount:0
#: selection:account.reconcile.model.template,match_amount:0
msgid "Is Between"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_is_follower
#: model:ir.model.fields,field_description:account.field_account_invoice__message_is_follower
#: model:ir.model.fields,field_description:account.field_account_payment__message_is_follower
msgid "Is Follower"
msgstr "Je pratilac"

#. module: account
#: selection:account.reconcile.model,match_amount:0
#: selection:account.reconcile.model.template,match_amount:0
msgid "Is Greater Than"
msgstr ""

#. module: account
#: selection:account.reconcile.model,match_amount:0
#: selection:account.reconcile.model.template,match_amount:0
msgid "Is Lower Than"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__printed
msgid "Is Printed"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__is_second_tax_price_included
msgid "Is Second Tax Included in Price"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__is_tax_price_included
msgid "Is Tax Included in Price"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line__is_rounding_line
msgid "Is a rounding line in case of cash rounding."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__is_difference_zero
msgid "Is zero"
msgstr "Je nula"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__default_credit_account_id
#: model:ir.model.fields,help:account.field_res_company__income_currency_exchange_account_id
msgid "It acts as a default account for credit amount"
msgstr "Zadani konto za potražni iznos"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__default_debit_account_id
#: model:ir.model.fields,help:account.field_res_company__expense_currency_exchange_account_id
msgid "It acts as a default account for debit amount"
msgstr "Uobičajeni konto za dugovni iznos"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__alias_name
msgid "It creates draft vendor bill by sending an email."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__reconciled
msgid ""
"It indicates that the invoice has been paid and the journal entry of the "
"invoice has been reconciled with one or several journal entries of payment."
msgstr ""
"Označava da je faktura plaćena i da je dnevnički zapis fakture zatvoren sa "
"jednim ili više izvoda."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__sent
msgid "It indicates that the invoice has been sent."
msgstr "Označava da je faktura poslana"

#. module: account
#: code:addons/account/models/account_move.py:971
#, python-format
msgid ""
"It is mandatory to specify an account and a journal to create a write-off."
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:585
#, python-format
msgid ""
"It is not allowed to delete a payment that already created a journal entry "
"since it would create a gap in the numbering. You should create the journal "
"entry again and cancel it thanks to a regular revert."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Items"
msgstr "Stavke"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "January"
msgstr "Januar"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:200
#: code:addons/account/static/src/xml/account_reconciliation.xml:283
#: model:ir.model,name:account.model_account_journal
#: model:ir.model.fields,field_description:account.field_account_bank_statement__journal_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__journal_id
#: model:ir.model.fields,field_description:account.field_account_invoice__journal_id
#: model:ir.model.fields,field_description:account.field_account_invoice_import_wizard__journal_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report__journal_id
#: model:ir.model.fields,field_description:account.field_account_move__journal_id
#: model:ir.model.fields,field_description:account.field_account_move_line__journal_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__journal_id
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__linked_journal_id
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__journal_id
#: model_terms:ir.ui.view,arch_db:account.report_journal
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_search
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_from_invoices
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_invoice_form
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_search
#, python-format
msgid "Journal"
msgstr "Dnevnik knjiženja"

#. module: account
#: code:addons/account/models/account_bank_statement.py:256
#: model:ir.actions.act_window,name:account.action_move_journal_line
#: model:ir.actions.act_window,name:account.action_move_select
#: model:ir.model,name:account.model_account_move
#: model:ir.ui.menu,name:account.menu_action_move_journal_line_form
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_form
#: model_terms:ir.ui.view,arch_db:account.view_move_tree
#, python-format
msgid "Journal Entries"
msgstr "Dnevnički zapisi"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Journal Entries by Date"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__move_id
#: model:ir.model.fields,field_description:account.field_account_move_line__move_id
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Journal Entry"
msgstr "Dnevnički zapis"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__move_name
#: model:ir.model.fields,field_description:account.field_account_invoice__move_name
#: model:ir.model.fields,field_description:account.field_account_payment__move_name
msgid "Journal Entry Name"
msgstr "Naziv dnevničkog zapisa"

#. module: account
#: selection:account.print.journal,sort_selection:0
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "Journal Entry Number"
msgstr "Broj dnevničkih zapisa"

#. module: account
#: model:ir.model,name:account.model_account_move_line
#: model:ir.model.fields,field_description:account.field_account_analytic_line__move_id
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
msgid "Journal Item"
msgstr "Stavka dnevnika"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment__writeoff_label
#: model:ir.model.fields,field_description:account.field_account_payment__writeoff_label
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__label
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__label
#: model:ir.model.fields,field_description:account.field_account_register_payments__writeoff_label
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Journal Item Label"
msgstr "Naslov dnevničkog zapisa"

#. module: account
#: code:addons/account/models/account_payment.py:529
#: code:addons/account/models/reconciliation_widget.py:196
#: model:ir.actions.act_window,name:account.act_account_journal_2_account_move_line
#: model:ir.actions.act_window,name:account.act_account_move_to_account_move_line_open
#: model:ir.actions.act_window,name:account.action_account_moves_all_a
#: model:ir.actions.act_window,name:account.action_account_moves_all_tree
#: model:ir.actions.act_window,name:account.action_move_line_graph
#: model:ir.actions.act_window,name:account.action_move_line_graph_cash_basis
#: model:ir.actions.act_window,name:account.action_move_line_graph_cash_basis_posted
#: model:ir.actions.act_window,name:account.action_move_line_graph_posted
#: model:ir.actions.act_window,name:account.action_move_line_select
#: model:ir.actions.act_window,name:account.action_move_line_select_by_partner
#: model:ir.actions.act_window,name:account.action_move_line_select_by_type
#: model:ir.actions.act_window,name:account.action_move_line_select_tax_audit
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__journal_entry_ids
#: model:ir.model.fields,field_description:account.field_account_move__line_ids
#: model:ir.model.fields,field_description:account.field_res_partner__journal_item_count
#: model:ir.ui.menu,name:account.menu_action_account_moves_all
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_form
#: model_terms:ir.ui.view,arch_db:account.view_move_form
#: model_terms:ir.ui.view,arch_db:account.view_move_line_pivot
#: model_terms:ir.ui.view,arch_db:account.view_move_line_tree
#, python-format
msgid "Journal Items"
msgstr "Stavke dnevnika"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_action.js:326
#: model:ir.actions.client,name:account.action_manual_reconcile
#, python-format
msgid "Journal Items to Reconcile"
msgstr "Stavke dnevnika za zatvaranje"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__name
msgid "Journal Name"
msgstr "Naziv naloga za knjiženje"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Journal items where matching number isn't set"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_company__account_opening_journal_id
msgid ""
"Journal where the opening entry of this company's accounting has been "
"posted."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_import_wizard__journal_id
msgid "Journal where to generate the bills"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__journal_currency_id
msgid "Journal's Currency"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_account_journal_form
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__journal_ids
#: model:ir.model.fields,field_description:account.field_account_common_report__journal_ids
#: model:ir.model.fields,field_description:account.field_account_print_journal__journal_ids
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_journal_ids
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_journal_ids
#: model:ir.ui.menu,name:account.menu_action_account_journal_form
msgid "Journals"
msgstr "Dnevnici"

#. module: account
#: model:ir.actions.report,name:account.action_report_journal
msgid "Journals Audit"
msgstr ""

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "July"
msgstr "Jul"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "June"
msgstr "Jun"

#. module: account
#: selection:res.company,account_dashboard_onboarding_state:0
#: selection:res.company,account_invoice_onboarding_state:0
#: selection:res.company,account_onboarding_invoice_layout_state:0
#: selection:res.company,account_onboarding_sale_tax_state:0
#: selection:res.company,account_onboarding_sample_invoice_state:0
#: selection:res.company,account_setup_bank_data_state:0
#: selection:res.company,account_setup_coa_state:0
#: selection:res.company,account_setup_fy_data_state:0
msgid "Just done"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__reason
msgid "Justification"
msgstr "Opravdanje"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__kanban_dashboard
msgid "Kanban Dashboard"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__kanban_dashboard_graph
msgid "Kanban Dashboard Graph"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Keep empty for no control"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__date_invoice
msgid "Keep empty to use the current date"
msgstr "Ostaviti prazno da se koristi današnji datum"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__date
msgid "Keep empty to use the invoice date."
msgstr ""

#. module: account
#: selection:account.abstract.payment,payment_difference_handling:0
#: selection:account.payment,payment_difference_handling:0
#: selection:account.register.payments,payment_difference_handling:0
msgid "Keep open"
msgstr "Drži otvoreno"

#. module: account
#: model:ir.model.fields,help:account.field_product_template__property_account_income_id
msgid ""
"Keep this field empty to use the default value from the product category."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_product_template__property_account_expense_id
msgid ""
"Keep this field empty to use the default value from the product category. If"
" anglo-saxon accounting with automated valuation method is configured, the "
"expense account on the product category will be used."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_digest_digest__kpi_account_total_revenue_value
msgid "Kpi Account Total Revenue Value"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:560
#: code:addons/account/static/src/xml/account_reconciliation.xml:204
#: code:addons/account/static/src/xml/account_reconciliation.xml:284
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__name
#: model:ir.model.fields,field_description:account.field_account_move_line__name
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_label
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_label
#: model_terms:ir.ui.view,arch_db:account.report_journal
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_from_invoices
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_invoice_form
#, python-format
msgid "Label"
msgstr "Naljepnica"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_label_param
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_label_param
msgid "Label Parameter"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__description
msgid "Label on Invoices"
msgstr "Oznake na fakturama"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment____last_update
#: model:ir.model.fields,field_description:account.field_account_account____last_update
#: model:ir.model.fields,field_description:account.field_account_account_tag____last_update
#: model:ir.model.fields,field_description:account.field_account_account_template____last_update
#: model:ir.model.fields,field_description:account.field_account_account_type____last_update
#: model:ir.model.fields,field_description:account.field_account_bank_statement____last_update
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox____last_update
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance____last_update
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line____last_update
#: model:ir.model.fields,field_description:account.field_account_cash_rounding____last_update
#: model:ir.model.fields,field_description:account.field_account_cashbox_line____last_update
#: model:ir.model.fields,field_description:account.field_account_chart_template____last_update
#: model:ir.model.fields,field_description:account.field_account_common_journal_report____last_update
#: model:ir.model.fields,field_description:account.field_account_common_report____last_update
#: model:ir.model.fields,field_description:account.field_account_financial_year_op____last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position____last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account____last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template____last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax____last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template____last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template____last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_year____last_update
#: model:ir.model.fields,field_description:account.field_account_full_reconcile____last_update
#: model:ir.model.fields,field_description:account.field_account_group____last_update
#: model:ir.model.fields,field_description:account.field_account_incoterms____last_update
#: model:ir.model.fields,field_description:account.field_account_invoice____last_update
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm____last_update
#: model:ir.model.fields,field_description:account.field_account_invoice_import_wizard____last_update
#: model:ir.model.fields,field_description:account.field_account_invoice_line____last_update
#: model:ir.model.fields,field_description:account.field_account_invoice_refund____last_update
#: model:ir.model.fields,field_description:account.field_account_invoice_report____last_update
#: model:ir.model.fields,field_description:account.field_account_invoice_send____last_update
#: model:ir.model.fields,field_description:account.field_account_invoice_tax____last_update
#: model:ir.model.fields,field_description:account.field_account_journal____last_update
#: model:ir.model.fields,field_description:account.field_account_move____last_update
#: model:ir.model.fields,field_description:account.field_account_move_line____last_update
#: model:ir.model.fields,field_description:account.field_account_move_reversal____last_update
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile____last_update
#: model:ir.model.fields,field_description:account.field_account_payment____last_update
#: model:ir.model.fields,field_description:account.field_account_payment_method____last_update
#: model:ir.model.fields,field_description:account.field_account_payment_term____last_update
#: model:ir.model.fields,field_description:account.field_account_payment_term_line____last_update
#: model:ir.model.fields,field_description:account.field_account_print_journal____last_update
#: model:ir.model.fields,field_description:account.field_account_reconcile_model____last_update
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template____last_update
#: model:ir.model.fields,field_description:account.field_account_reconciliation_widget____last_update
#: model:ir.model.fields,field_description:account.field_account_register_payments____last_update
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config____last_update
#: model:ir.model.fields,field_description:account.field_account_tax____last_update
#: model:ir.model.fields,field_description:account.field_account_tax_group____last_update
#: model:ir.model.fields,field_description:account.field_account_tax_template____last_update
#: model:ir.model.fields,field_description:account.field_account_unreconcile____last_update
#: model:ir.model.fields,field_description:account.field_cash_box_in____last_update
#: model:ir.model.fields,field_description:account.field_cash_box_out____last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_agedpartnerbalance____last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_invoice_with_payments____last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_journal____last_update
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard____last_update
#: model:ir.model.fields,field_description:account.field_validate_account_move____last_update
msgid "Last Modified on"
msgstr "Zadnje mijenjano"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:125
#, python-format
msgid "Last Reconciliation:"
msgstr "Zadnje zatvaranje:"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__write_uid
#: model:ir.model.fields,field_description:account.field_account_account_tag__write_uid
#: model:ir.model.fields,field_description:account.field_account_account_template__write_uid
#: model:ir.model.fields,field_description:account.field_account_account_type__write_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement__write_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox__write_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance__write_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__write_uid
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__write_uid
#: model:ir.model.fields,field_description:account.field_account_cashbox_line__write_uid
#: model:ir.model.fields,field_description:account.field_account_chart_template__write_uid
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__write_uid
#: model:ir.model.fields,field_description:account.field_account_common_report__write_uid
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account__write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template__write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax__write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template__write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_year__write_uid
#: model:ir.model.fields,field_description:account.field_account_full_reconcile__write_uid
#: model:ir.model.fields,field_description:account.field_account_group__write_uid
#: model:ir.model.fields,field_description:account.field_account_incoterms__write_uid
#: model:ir.model.fields,field_description:account.field_account_invoice__write_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm__write_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_import_wizard__write_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_line__write_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_refund__write_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_send__write_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_tax__write_uid
#: model:ir.model.fields,field_description:account.field_account_journal__write_uid
#: model:ir.model.fields,field_description:account.field_account_move__write_uid
#: model:ir.model.fields,field_description:account.field_account_move_line__write_uid
#: model:ir.model.fields,field_description:account.field_account_move_reversal__write_uid
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__write_uid
#: model:ir.model.fields,field_description:account.field_account_payment__write_uid
#: model:ir.model.fields,field_description:account.field_account_payment_method__write_uid
#: model:ir.model.fields,field_description:account.field_account_payment_term__write_uid
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__write_uid
#: model:ir.model.fields,field_description:account.field_account_print_journal__write_uid
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__write_uid
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__write_uid
#: model:ir.model.fields,field_description:account.field_account_register_payments__write_uid
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__write_uid
#: model:ir.model.fields,field_description:account.field_account_tax__write_uid
#: model:ir.model.fields,field_description:account.field_account_tax_group__write_uid
#: model:ir.model.fields,field_description:account.field_account_tax_template__write_uid
#: model:ir.model.fields,field_description:account.field_account_unreconcile__write_uid
#: model:ir.model.fields,field_description:account.field_cash_box_in__write_uid
#: model:ir.model.fields,field_description:account.field_cash_box_out__write_uid
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__write_uid
#: model:ir.model.fields,field_description:account.field_validate_account_move__write_uid
msgid "Last Updated by"
msgstr "Zadnji ažurirao"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__write_date
#: model:ir.model.fields,field_description:account.field_account_account_tag__write_date
#: model:ir.model.fields,field_description:account.field_account_account_template__write_date
#: model:ir.model.fields,field_description:account.field_account_account_type__write_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement__write_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox__write_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance__write_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__write_date
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__write_date
#: model:ir.model.fields,field_description:account.field_account_cashbox_line__write_date
#: model:ir.model.fields,field_description:account.field_account_chart_template__write_date
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__write_date
#: model:ir.model.fields,field_description:account.field_account_common_report__write_date
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account__write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template__write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax__write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template__write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_year__write_date
#: model:ir.model.fields,field_description:account.field_account_full_reconcile__write_date
#: model:ir.model.fields,field_description:account.field_account_group__write_date
#: model:ir.model.fields,field_description:account.field_account_incoterms__write_date
#: model:ir.model.fields,field_description:account.field_account_invoice__write_date
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm__write_date
#: model:ir.model.fields,field_description:account.field_account_invoice_import_wizard__write_date
#: model:ir.model.fields,field_description:account.field_account_invoice_line__write_date
#: model:ir.model.fields,field_description:account.field_account_invoice_refund__write_date
#: model:ir.model.fields,field_description:account.field_account_invoice_send__write_date
#: model:ir.model.fields,field_description:account.field_account_invoice_tax__write_date
#: model:ir.model.fields,field_description:account.field_account_journal__write_date
#: model:ir.model.fields,field_description:account.field_account_move__write_date
#: model:ir.model.fields,field_description:account.field_account_move_line__write_date
#: model:ir.model.fields,field_description:account.field_account_move_reversal__write_date
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__write_date
#: model:ir.model.fields,field_description:account.field_account_payment__write_date
#: model:ir.model.fields,field_description:account.field_account_payment_method__write_date
#: model:ir.model.fields,field_description:account.field_account_payment_term__write_date
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__write_date
#: model:ir.model.fields,field_description:account.field_account_print_journal__write_date
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__write_date
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__write_date
#: model:ir.model.fields,field_description:account.field_account_register_payments__write_date
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__write_date
#: model:ir.model.fields,field_description:account.field_account_tax__write_date
#: model:ir.model.fields,field_description:account.field_account_tax_group__write_date
#: model:ir.model.fields,field_description:account.field_account_tax_template__write_date
#: model:ir.model.fields,field_description:account.field_account_unreconcile__write_date
#: model:ir.model.fields,field_description:account.field_cash_box_in__write_date
#: model:ir.model.fields,field_description:account.field_cash_box_out__write_date
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__write_date
#: model:ir.model.fields,field_description:account.field_validate_account_move__write_date
msgid "Last Updated on"
msgstr "Zadnje ažurirano"

#. module: account
#: model:ir.model.fields,help:account.field_res_partner__last_time_entries_checked
msgid ""
"Last time the invoices & payments matching was performed for this partner. "
"It is set either if there's not at least an unreconciled debit and an "
"unreconciled credit or if you click the \"Done\" button."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_account__last_time_entries_checked
msgid ""
"Last time the invoices & payments matching was performed on this account. It"
" is set either if there's not at least an unreconciled debit and an "
"unreconciled credit Or if you click the \"Done\" button."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Late Activities"
msgstr "Aktivnosti u kašnjenju"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__last_time_entries_checked
#: model:ir.model.fields,field_description:account.field_res_partner__last_time_entries_checked
msgid "Latest Invoices & Payments Matching Date"
msgstr "Poljednji datum poređenja Faktura i Uplata"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__layout
msgid "Layout"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_position_form
msgid "Legal Notes..."
msgstr "Pravne zabilješke..."

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position__note
msgid "Legal mentions that have to be printed on the invoices."
msgstr ""

#. module: account
#: code:addons/account/models/account_invoice.py:219
#, python-format
msgid "Less Payment"
msgstr "Manje plaćeno"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Let your customers pay their invoices online"
msgstr ""

#. module: account
#: selection:account.account.type,internal_group:0
#: model_terms:ir.ui.view,arch_db:account.view_account_search
msgid "Liability"
msgstr "Obveza"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__nbr
msgid "Line Count"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__show_line_subtotals_tax_selection
msgid "Line Subtotals Tax Display"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Line subtotals tax display"
msgstr ""

#. module: account
#: selection:account.setup.bank.manual.config,create_or_link_option:0
msgid "Link to an existing journal"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__move_id
msgid "Link to the automatically generated Journal Items."
msgstr "Poveznica na automatski kreirane stavke dnevnika"

#. module: account
#: selection:account.account.type,type:0
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_search
msgid "Liquidity"
msgstr "Likvidnost"

#. module: account
#: code:addons/account/models/chart_template.py:154
#, python-format
msgid "Liquidity Transfer"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_chart_template__tax_template_ids
msgid "List of all the taxes that have to be installed by the wizard"
msgstr "Spisak svih poreza koji trebaju biti instalirani od strane čarobnjaka"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:33
#, python-format
msgid "Load more"
msgstr "Učitaj više"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:154
#, python-format
msgid "Load more... ("
msgstr "Učitaj više... ("

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__fiscalyear_lock_date
msgid "Lock Date"
msgstr "Datum zaključavanja"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__period_lock_date
msgid "Lock Date for Non-Advisers"
msgstr "Datum zaključavanja osim za Računovodstvene Savjetnike"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__is_log
msgid "Log an Internal Note"
msgstr "Zabilježi internu zabilješku"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_invoice_layout_step
msgid "Looks great!"
msgstr ""

#. module: account
#: code:addons/account/models/account_bank_statement.py:175
#, python-format
msgid "Loss"
msgstr "Gubitak"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__loss_account_id
msgid "Loss Account"
msgstr "Konto gubitka"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__expense_currency_exchange_account_id
#: model:ir.model.fields,field_description:account.field_res_company__expense_currency_exchange_account_id
msgid "Loss Exchange Rate Account"
msgstr "Konto negativnih kursnih razlika"

#. module: account
#: code:addons/account/models/chart_template.py:411
#, python-format
msgid "MISC"
msgstr "RAZNO"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__mail_activity_type_id
msgid "Mail Activity Type"
msgstr "Tip email aktivnosti"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__mailing_list_ids
msgid "Mailing List"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_main_attachment_id
#: model:ir.model.fields,field_description:account.field_account_invoice__message_main_attachment_id
#: model:ir.model.fields,field_description:account.field_account_payment__message_main_attachment_id
msgid "Main Attachment"
msgstr "Glavna zakačka"

#. module: account
#: model:ir.model.fields,help:account.field_res_config_settings__currency_id
msgid "Main currency of the company."
msgstr "Glavna valuta kompanije"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Main currency of your company"
msgstr "Glavna valuta vaše kompanije"

#. module: account
#: model:ir.ui.menu,name:account.account_management_menu
#: model:ir.ui.menu,name:account.account_reports_management_menu
#: model:ir.ui.menu,name:account.menu_finance_entries_management
msgid "Management"
msgstr "Upravljanje"

#. module: account
#: model:account.payment.method,name:account.account_payment_method_manual_in
#: model:account.payment.method,name:account.account_payment_method_manual_out
#: model:ir.model.fields,field_description:account.field_account_invoice_tax__manual
msgid "Manual"
msgstr "Ručno"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_invoice_tax_form
#: model_terms:ir.ui.view,arch_db:account.view_invoice_tax_tree
msgid "Manual Invoice Taxes"
msgstr "Ručni porezi fakture"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__inbound_payment_method_ids
msgid ""
"Manual: Get paid by cash, check or any other method outside of Odoo.\n"
"Electronic: Get paid automatically through a payment acquirer by requesting a transaction on a card saved by the customer when buying or subscribing online (payment token).\n"
"Batch Deposit: Encase several customer checks at once by generating a batch deposit to submit to your bank. When encoding the bank statement in Odoo,you are suggested to reconcile the transaction with the batch deposit. Enable this option from the settings."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_abstract_payment__payment_method_id
#: model:ir.model.fields,help:account.field_account_payment__payment_method_id
#: model:ir.model.fields,help:account.field_account_register_payments__payment_method_id
msgid ""
"Manual: Get paid by cash, check or any other method outside of Odoo.\n"
"Electronic: Get paid automatically through a payment acquirer by requesting a transaction on a card saved by the customer when buying or subscribing online (payment token).\n"
"Check: Pay bill by check and print it from Odoo.\n"
"Batch Deposit: Encase several customer checks at once by generating a batch deposit to submit to your bank. When encoding the bank statement in Odoo, you are suggested to reconcile the transaction with the batch deposit.To enable batch deposit, module account_batch_payment must be installed.\n"
"SEPA Credit Transfer: Pay bill from a SEPA Credit Transfer file you submit to your bank. To enable sepa credit transfer, module account_sepa must be installed "
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__outbound_payment_method_ids
msgid ""
"Manual:Pay bill by cash or any other method outside of Odoo.\n"
"Check:Pay bill by check and print it from Odoo.\n"
"SEPA Credit Transfer: Pay bill from a SEPA Credit Transfer file you submit to your bank. Enable this option from the settings."
msgstr ""

#. module: account
#: selection:account.reconcile.model,rule_type:0
#: selection:account.reconcile.model.template,rule_type:0
#: code:addons/account/models/account_reconcile_model.py:19
#: code:addons/account/models/chart_template.py:932
#, python-format
msgid "Manually create a write-off on clicked button."
msgstr ""

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "March"
msgstr "Mart"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Margin Analysis"
msgstr "Analiza marže"

#. module: account
#: selection:account.abstract.payment,payment_difference_handling:0
#: selection:account.payment,payment_difference_handling:0
#: selection:account.register.payments,payment_difference_handling:0
msgid "Mark invoice as fully paid"
msgstr "Označi fakture kao plaćene u potpunosti"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__mass_mailing_id
msgid "Mass Mailing"
msgstr "Masovno slanje pošte"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__mass_mailing_campaign_id
msgid "Mass Mailing Campaign"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__mass_mailing_name
msgid "Mass Mailing Name"
msgstr ""

#. module: account
#: selection:account.reconcile.model,match_label:0
#: selection:account.reconcile.model.template,match_label:0
msgid "Match Regex"
msgstr ""

#. module: account
#: selection:account.reconcile.model,rule_type:0
#: selection:account.reconcile.model.template,rule_type:0
#: code:addons/account/models/account_reconcile_model.py:21
#: code:addons/account/models/chart_template.py:934
#, python-format
msgid "Match existing invoices/bills."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__matched_credit_ids
msgid "Matched Credit"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__matched_debit_ids
msgid "Matched Debit"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_full_reconcile__reconciled_line_ids
#: model_terms:ir.ui.view,arch_db:account.view_full_reconcile_form
msgid "Matched Journal Items"
msgstr "Usklađene stavke dnevnika"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_full_reconcile_form
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
msgid "Matching"
msgstr "Usklađivanje"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__full_reconcile_id
msgid "Matching Number"
msgstr "Broj usklađivanja"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__max_date
msgid "Max Date of Matched Lines"
msgstr ""

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "May"
msgstr "Maj"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment__communication
#: model:ir.model.fields,field_description:account.field_account_payment__communication
#: model:ir.model.fields,field_description:account.field_account_register_payments__communication
msgid "Memo"
msgstr "Zabilješka"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_from_invoices
msgid "Memo will be computed from invoices"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:64
#, python-format
msgid "Memo:"
msgstr "Zabilješka:"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_has_error
#: model:ir.model.fields,field_description:account.field_account_invoice__message_has_error
#: model:ir.model.fields,field_description:account.field_account_payment__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__record_name
msgid "Message Record Name"
msgstr "Naziv zapisa poruke"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__invoice_warn_msg
msgid "Message for Invoice"
msgstr "Poruka za Fakturu"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__message_type
msgid ""
"Message type: email for email message, notification for system message, "
"comment for other messages such as user replies"
msgstr ""
"Vrsta poruke: e-mail za e-mail poruke, obavjest za sistemske poruke, "
"komentari za druge poruke poput korisničkih odgovora"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__message_id
msgid "Message unique identifier"
msgstr "Jedinstveni identifikator poruke"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__message_id
msgid "Message-Id"
msgstr "Id-poruke"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_ids
#: model:ir.model.fields,field_description:account.field_account_invoice__message_ids
#: model:ir.model.fields,field_description:account.field_account_payment__message_ids
msgid "Messages"
msgstr "Poruke"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_form
msgid "Minus Cost of Revenue"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_form
msgid "Minus Credit Card Accounts"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_form
msgid "Minus Current Liabilities"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_form
msgid "Minus Expenses"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_form
msgid "Minus Non-Current Liabilities"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_form
msgid "Minus Payable Accounts"
msgstr ""

#. module: account
#: selection:account.journal,type:0
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_search
msgid "Miscellaneous"
msgstr "Ostalo"

#. module: account
#: code:addons/account/models/chart_template.py:411
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#, python-format
msgid "Miscellaneous Operations"
msgstr "Ostale operacije"

#. module: account
#: sql_constraint:account.invoice.line:0
msgid "Missing required account on accountable invoice line."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__moderator_id
msgid "Moderated By"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__moderation_status
msgid "Moderation Status"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:175
#, python-format
msgid "Modify models"
msgstr ""

#. module: account
#: selection:account.cash.rounding,strategy:0
msgid "Modify tax amount"
msgstr ""

#. module: account
#: selection:account.invoice.refund,filter_refund:0
msgid "Modify: create credit note, reconcile and create a new draft invoice"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Monitor your product margins from invoices"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_journal
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Move"
msgstr "Kretanje"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__move_line_ids
msgid "Move Line"
msgstr "Stavka prijenosa"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__move_line_count
msgid "Move Line Count"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__move_reconciled
msgid "Move Reconciled"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:1144
#, python-format
msgid "Move name (id): %s (%s)"
msgstr "Naziv knjiženja (id): %s (%s)"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment__multi
#: model:ir.model.fields,field_description:account.field_account_payment__multi
#: model:ir.model.fields,field_description:account.field_account_register_payments__multi
msgid "Multi"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Multi-Currencies"
msgstr "Multi-Valute"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "My Activities"
msgstr "Moje aktivnosti"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "My Invoices"
msgstr "Moje fakture"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_form
msgid "NET ASSETS"
msgstr "NETO IMOVINA"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_form
msgid "NET PROFIT"
msgstr "NETO DOBIT"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__name
#: model:ir.model.fields,field_description:account.field_account_account_tag__name
#: model:ir.model.fields,field_description:account.field_account_account_template__name
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__name
#: model:ir.model.fields,field_description:account.field_account_chart_template__name
#: model:ir.model.fields,field_description:account.field_account_fiscal_year__name
#: model:ir.model.fields,field_description:account.field_account_group__name
#: model:ir.model.fields,field_description:account.field_account_incoterms__name
#: model:ir.model.fields,field_description:account.field_account_payment__name
#: model:ir.model.fields,field_description:account.field_account_payment_method__name
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__name
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__bank_name
#: model:ir.model.fields,field_description:account.field_account_tax_group__name
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "Name"
msgstr "Naziv:"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__record_name
msgid "Name get of the related document."
msgstr "Naziv preuzet iz povezanog dokumenta"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:52
#, python-format
msgid "Name:"
msgstr "Naziv:"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__narration
msgid "Narration"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Navigate easily through reports and see what is behind the numbers"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__needaction
#: model:ir.model.fields,help:account.field_account_invoice_send__needaction
msgid "Need Action"
msgstr "Potrebna akcija"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__need_moderation
msgid "Need moderation"
msgstr ""

#. module: account
#. openerp-web
#: selection:account.bank.statement,state:0
#: code:addons/account/static/src/xml/account_reconciliation.xml:243
#, python-format
msgid "New"
msgstr "Novi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__new_journal_name
msgid "New Journal Name"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "New Transactions"
msgstr "Nove transakcije"

#. module: account
#: code:addons/account/models/account_move.py:1121
#, python-format
msgid "New expected payment date: "
msgstr "Novi datum očekivanog plaćanja:"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Krajnji rok za sljedeću aktivnost"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__activity_summary
msgid "Next Activity Summary"
msgstr "Pregled sljedeće aktivnosti"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__activity_type_id
msgid "Next Activity Type"
msgstr "Tip sljedeće aktivnosti"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__sequence_number_next
#: model:ir.model.fields,field_description:account.field_account_journal__sequence_number_next
msgid "Next Number"
msgstr "Sljedeći broj"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__sequence_number_next_prefix
msgid "Next Number Prefix"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__blocked
msgid "No Follow-up"
msgstr "Bez opomena"

#. module: account
#: selection:res.partner,invoice_warn:0
msgid "No Message"
msgstr "Bez Poruka"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:67
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:137
#, python-format
msgid "No Title"
msgstr ""

#. module: account
#: code:addons/account/models/account_invoice.py:897
#, python-format
msgid ""
"No account was found to create the invoice, be sure you have installed a "
"chart of account."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__no_auto_thread
msgid "No threading for answers"
msgstr "Nema niti za odgovore"

#. module: account
#: model:ir.model.fields,help:account.field_res_company__fiscalyear_lock_date
msgid ""
"No users, including Advisers, can edit accounts prior to and inclusive of "
"this date. Use it for fiscal year locking for example."
msgstr ""
"Nijedan korisnik, uključujući savjetnike, ne može uređivati knjiženja prije "
"uključujući ovaj datum. Koristite ovo da na primjer zaključate fiskalnu "
"godinu."

#. module: account
#: model:ir.model.fields,help:account.field_account_chart_template__code_digits
msgid "No. of Digits to use for account code"
msgstr "Broj znamenki za upotrebu u šifri računa"

#. module: account
#: model:account.account.type,name:account.data_account_type_non_current_assets
msgid "Non-current Assets"
msgstr "Stalna sredstva"

#. module: account
#: model:account.account.type,name:account.data_account_type_non_current_liabilities
msgid "Non-current Liabilities"
msgstr "Dugoročne obaveze"

#. module: account
#: selection:account.tax,type_tax_use:0
#: selection:account.tax.template,type_tax_use:0
msgid "None"
msgstr "Ništa"

#. module: account
#: selection:res.partner,trust:0
msgid "Normal Debtor"
msgstr "Normalan platioc"

#. module: account
#: selection:account.reconcile.model,match_label:0
#: selection:account.reconcile.model.template,match_label:0
msgid "Not Contains"
msgstr ""

#. module: account
#: selection:res.company,account_dashboard_onboarding_state:0
#: selection:res.company,account_invoice_onboarding_state:0
#: selection:res.company,account_onboarding_invoice_layout_state:0
#: selection:res.company,account_onboarding_sale_tax_state:0
#: selection:res.company,account_onboarding_sample_invoice_state:0
#: selection:res.company,account_setup_bank_data_state:0
#: selection:res.company,account_setup_coa_state:0
#: selection:res.company,account_setup_fy_data_state:0
msgid "Not done"
msgstr ""

#. module: account
#. openerp-web
#: selection:account.invoice.line,display_type:0
#: code:addons/account/static/src/xml/account_reconciliation.xml:308
#: model:ir.model.fields,field_description:account.field_account_account_template__note
#: model_terms:ir.ui.view,arch_db:account.view_invoice_line_form
#, python-format
msgid "Note"
msgstr "Zabilješka"

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_invoice_out_refund
msgid ""
"Note that the easiest way to create a credit note is to do it directly\n"
"                from the customer invoice."
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_invoice_in_refund
msgid ""
"Note that the easiest way to create a vendor credit note it to do it "
"directly from the vendor bill."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__note
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__note
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__note
#: model_terms:ir.ui.view,arch_db:account.view_account_template_form
msgid "Notes"
msgstr "Zabilješke"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:37
#, python-format
msgid "Nothing to do!"
msgstr "Nema ništa za uraditi!"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__notification_ids
msgid "Notifications"
msgstr "Obavještenja"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__notify
msgid "Notify followers"
msgstr "Obavjesti pratioce"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__notify
msgid "Notify followers of the document (mass post only)"
msgstr "Obavjesti pratioce dokumenta (samo masovni postovi)"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "November"
msgstr "Novembar"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_full_reconcile__name
#: model:ir.model.fields,field_description:account.field_account_invoice__number
#: model:ir.model.fields,field_description:account.field_account_move__name
msgid "Number"
msgstr "Broj"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Number (Move)"
msgstr "Broj (kretanja)"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_needaction_counter
#: model:ir.model.fields,field_description:account.field_account_invoice__message_needaction_counter
#: model:ir.model.fields,field_description:account.field_account_payment__message_needaction_counter
msgid "Number of Actions"
msgstr "Broj akcija"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cashbox_line__number
msgid "Number of Coins/Bills"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__days
msgid "Number of Days"
msgstr "Broj dana"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_has_error_counter
#: model:ir.model.fields,field_description:account.field_account_invoice__message_has_error_counter
#: model:ir.model.fields,field_description:account.field_account_payment__message_has_error_counter
msgid "Number of error"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__message_needaction_counter
#: model:ir.model.fields,help:account.field_account_invoice__message_needaction_counter
#: model:ir.model.fields,help:account.field_account_payment__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Broj poruka koje zahtjevaju neku akciju"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__message_has_error_counter
#: model:ir.model.fields,help:account.field_account_invoice__message_has_error_counter
#: model:ir.model.fields,help:account.field_account_payment__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__message_unread_counter
#: model:ir.model.fields,help:account.field_account_invoice__message_unread_counter
#: model:ir.model.fields,help:account.field_account_payment__message_unread_counter
msgid "Number of unread messages"
msgstr "Broj nepročitanih poruka"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "OFX Import"
msgstr ""

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "October"
msgstr "Oktobar"

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_bank_statement_line
msgid ""
"Odoo allows you to reconcile a statement line directly with\n"
"                the related sale or puchase invoice(s)."
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_bank_statement_tree
msgid ""
"Odoo allows you to reconcile a statement line directly with\n"
"                the related sale or puchase invoices."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__tax_line_grouping_key
msgid "Old Taxes"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_line_form
msgid "On the"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_confirm_view
msgid ""
"Once draft invoices are confirmed, you will not be able\n"
"                        to modify them. The invoices will receive a unique\n"
"                        number and journal items will be created in your chart\n"
"                        of accounts."
msgstr ""
"Nakon potvrđivanja fakture u statusu 'U pripremi' nećete ih više moći\n"
"                        uređivati. Fakture će dobiti jedinstveni broj te će se \n"
"                        kreirati stavke u knjiženja."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Once installed, set 'Bank Feeds' to 'File Import' in bank account "
"settings.This adds a button to import from the Accounting dashboard."
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:599
#, python-format
msgid "Only a draft payment can be posted."
msgstr ""

#. module: account
#: code:addons/account/models/chart_template.py:185
#, python-format
msgid "Only administrators can load a charf of accounts"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_company__period_lock_date
msgid ""
"Only users with the 'Adviser' role can edit accounts prior to and inclusive "
"of this date. Use it for period locking inside an open fiscal year, for "
"example."
msgstr ""
"Samo korisnici sa 'Savjetnik' grupom može uređivati knjiženja prije i "
"uključujući ovaj datum. Koristite ovu opciju na primjer da zaključate "
"otvorenu fiskalnu godinu."

#. module: account
#: selection:account.invoice,state:0 selection:account.invoice.report,state:0
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Open"
msgstr "Otvori"

#. module: account
#: code:addons/account/models/account_reconcile_model.py:274
#, python-format
msgid "Open Balance"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_model.js:704
#: code:addons/account/static/src/xml/account_reconciliation.xml:137
#, python-format
msgid "Open balance"
msgstr "Saldo otvaranja"

#. module: account
#: model:ir.model,name:account.model_account_financial_year_op
msgid "Opening Balance of Financial Year"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__opening_date
#: model:ir.model.fields,field_description:account.field_res_company__account_opening_date
msgid "Opening Date"
msgstr "Datum otvaranja"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_opening_journal_id
msgid "Opening Journal"
msgstr "Dnevnik otvaranja"

#. module: account
#: code:addons/account/models/company.py:343
#: model:ir.model.fields,field_description:account.field_res_company__account_opening_move_id
#, python-format
msgid "Opening Journal Entry"
msgstr "Zapis dnevnika otvaranja"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__opening_move_posted
msgid "Opening Move Posted"
msgstr "Kretanje otvaranja proknjiženo"

#. module: account
#: model:ir.model.fields,help:account.field_account_cashbox_line__number
msgid "Opening Unit Numbers"
msgstr "Brojevi otvaranja"

#. module: account
#: code:addons/account/models/account.py:164
#, python-format
msgid "Opening balance"
msgstr "Početno stanje"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__opening_credit
msgid "Opening credit"
msgstr "Početno potraživanje"

#. module: account
#: model:ir.model.fields,help:account.field_account_account__opening_credit
msgid "Opening credit value for this account."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__opening_debit
msgid "Opening debit"
msgstr "Početno dugovanje"

#. module: account
#: model:ir.model.fields,help:account.field_account_account__opening_debit
msgid "Opening debit value for this account."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Operation Templates"
msgstr "Predlošci operacija"

#. module: account
#: code:addons/account/models/account_bank_statement.py:653
#, python-format
msgid ""
"Operation not allowed. Since your statement line already received a number "
"(%s), you cannot reconcile it entirely with existing journal entries "
"otherwise it would make a gap in the numbering. You should book an entry and"
" make a regular revert of it in case you want to cancel it."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Operations"
msgstr "Operacije"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_template__nocreate
msgid "Optional Create"
msgstr "Kreiraj opciono"

#. module: account
#: model:ir.model.fields,help:account.field_account_account__tag_ids
#: model:ir.model.fields,help:account.field_account_account_template__tag_ids
#: model:ir.model.fields,help:account.field_account_tax__tag_ids
#: model:ir.model.fields,help:account.field_account_tax_template__tag_ids
msgid "Optional tags you may want to assign for custom reporting"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__option
#: model_terms:ir.ui.view,arch_db:account.tax_adjustments_wizard
msgid "Options"
msgstr "Opcije"

#. module: account
#: code:addons/account/models/account_invoice.py:696
#, python-format
msgid ""
"Or set an <a data-oe-id=%s data-oe-model=\"account.journal\" "
"href=#id=%s&model=account.journal>email alias</a> to allow draft vendor "
"bills to be created upon reception of an email."
msgstr ""

#. module: account
#: code:addons/account/models/account_invoice.py:692
#, python-format
msgid ""
"Or share the email %s to your vendors: bills will be created automatically "
"upon mail reception."
msgstr ""

#. module: account
#: code:addons/account/models/account_invoice.py:694
#, python-format
msgid ""
"Or share the emails %s to your vendors: bills will be created automatically "
"upon mail reception."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__payment_id
msgid "Originator Payment"
msgstr "Izvorno plaćanje"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__tax_line_id
msgid "Originator tax"
msgstr "Izvorni porez"

#. module: account
#: model:account.account.type,name:account.data_account_type_other_income
msgid "Other Income"
msgstr "Ostali prihodi"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_form
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_form
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Other Info"
msgstr "Ostale informacije"

#. module: account
#: selection:account.payment.method,payment_type:0
msgid "Outbound"
msgstr "Izlazni"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__mail_server_id
msgid "Outgoing mail server"
msgstr "Izlazni server e-pošte"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__property_stock_account_output_categ_id
#: model:ir.model.fields,field_description:account.field_res_company__property_stock_account_output_categ_id
msgid "Output Account for Stock Valuation"
msgstr "Konto izlaza vrednovanja zalihe"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__outstanding_credits_debits_widget
msgid "Outstanding Credits Debits Widget"
msgstr ""

#. module: account
#: code:addons/account/models/account_invoice.py:133
#, python-format
msgid "Outstanding credits"
msgstr "Neplaćena potraživanja"

#. module: account
#: code:addons/account/models/account_invoice.py:136
#, python-format
msgid "Outstanding debits"
msgstr "Neplaćena dugovanja"

#. module: account
#: selection:account.invoice,activity_state:0
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Overdue"
msgstr "Dospjele"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__overdue_msg
msgid "Overdue Payments Message"
msgstr "Poruka o dospjelim plaćanjima."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Overdue invoices, maturity date passed"
msgstr "Dospjele fakture, valuta plaćanja istekla"

#. module: account
#: model:ir.ui.menu,name:account.menu_board_journal_1
msgid "Overview"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Package"
msgstr "Paket"

#. module: account
#: selection:account.invoice,state:0 selection:account.invoice.report,state:0
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model:mail.message.subtype,name:account.mt_invoice_paid
msgid "Paid"
msgstr "Plaćeno"

#. module: account
#: code:addons/account/models/account_payment.py:545
#, python-format
msgid "Paid Invoices"
msgstr "Plaćene fakture"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:28
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document_with_payments
#, python-format
msgid "Paid on"
msgstr "Plaćeno na"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__reconciled
msgid "Paid/Reconciled"
msgstr "Plaćeno/Zatvoreno"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_group__parent_id
msgid "Parent"
msgstr "Nasljeđeni"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__parent_id
msgid "Parent Chart Template"
msgstr "Nasljeđeni kontni plan"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__parent_id
msgid "Parent Message"
msgstr "Nadređena poruka"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_group__parent_path
msgid "Parent Path"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__parent_state
msgid "Parent State"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_partial_reconcile
msgid "Partial Reconcile"
msgstr "Djelomično zatvaranje"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:286
#: code:addons/account/static/src/xml/account_reconciliation.xml:303
#: model:ir.model.fields,field_description:account.field_account_abstract_payment__partner_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__partner_id
#: model:ir.model.fields,field_description:account.field_account_invoice__partner_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line__partner_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report__partner_id
#: model:ir.model.fields,field_description:account.field_account_move__partner_id
#: model:ir.model.fields,field_description:account.field_account_move_line__partner_id
#: model:ir.model.fields,field_description:account.field_account_payment__partner_id
#: model:ir.model.fields,field_description:account.field_account_register_payments__partner_id
#: model_terms:ir.ui.view,arch_db:account.report_journal
#: model_terms:ir.ui.view,arch_db:account.view_account_analytic_line_filter_inherit_account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
#, python-format
msgid "Partner"
msgstr "Partner"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__commercial_partner_id
msgid "Partner Company"
msgstr "Kompanija partnera"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__country_id
msgid "Partner Company's Country"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__contract_ids
msgid "Partner Contracts"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_partner
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_partner
msgid "Partner Is Set"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Partner Is Set & Matches"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__partner_name
msgid "Partner Name"
msgstr "Naziv partnera"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment__partner_type
#: model:ir.model.fields,field_description:account.field_account_payment__partner_type
#: model:ir.model.fields,field_description:account.field_account_register_payments__partner_type
msgid "Partner Type"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__needaction_partner_ids
msgid "Partners with Need Action"
msgstr "Partnere sa potrebnom akcijom"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:95
#, python-format
msgid "Past"
msgstr "Prošlost"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Pay your bills in one-click using Euro SEPA Service"
msgstr ""

#. module: account
#: model:account.account.type,name:account.data_account_type_payable
#: selection:account.account.type,type:0
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Payable"
msgstr "Potražni saldo-konto"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__property_account_payable_id
#: model_terms:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
msgid "Payable Account"
msgstr "Potražni saldo-konto"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_search
msgid "Payable Accounts"
msgstr "Potražna salda-konta"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__debit_limit
msgid "Payable Limit"
msgstr "Limit potražnog saldo-konta"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.product_template_form_view
msgid "Payables"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment__amount
#: model:ir.model.fields,field_description:account.field_account_payment__amount
#: model:ir.model.fields,field_description:account.field_account_register_payments__amount
msgid "Payment Amount"
msgstr "Iznos plaćanja"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment__payment_date
#: model:ir.model.fields,field_description:account.field_account_payment__payment_date
#: model:ir.model.fields,field_description:account.field_account_register_payments__payment_date
msgid "Payment Date"
msgstr "Datum plaćanja"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment__payment_difference
#: model:ir.model.fields,field_description:account.field_account_payment__payment_difference
#: model:ir.model.fields,field_description:account.field_account_register_payments__payment_difference
msgid "Payment Difference"
msgstr "Razlika plaćanja"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment__payment_difference_handling
#: model:ir.model.fields,field_description:account.field_account_payment__payment_difference_handling
#: model:ir.model.fields,field_description:account.field_account_register_payments__payment_difference_handling
msgid "Payment Difference Handling"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment__journal_id
#: model:ir.model.fields,field_description:account.field_account_payment__journal_id
#: model:ir.model.fields,field_description:account.field_account_register_payments__journal_id
msgid "Payment Journal"
msgstr "Dnevnik plaćanja"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_form
msgid "Payment Matching"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Payment Method"
msgstr "Metoda plaćanja"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment__payment_method_id
#: model:ir.model.fields,field_description:account.field_account_payment__payment_method_id
#: model:ir.model.fields,field_description:account.field_account_register_payments__payment_method_id
msgid "Payment Method Type"
msgstr "Tip metode plaćanja"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_bank_journal_form
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Payment Method Types"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:60
#, python-format
msgid "Payment Method:"
msgstr "Metoda plaćanja:"

#. module: account
#: model:ir.model,name:account.model_account_payment_method
msgid "Payment Methods"
msgstr "Metoda plaćanja"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__payment_move_line_ids
msgid "Payment Move Lines"
msgstr "Stavke plaćanja"

#. module: account
#: model:ir.actions.report,name:account.action_report_payment_receipt
msgid "Payment Receipt"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "Payment Receipt:"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__reference
msgid "Payment Ref."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__payment_reference
msgid "Payment Reference"
msgstr "Referenca plaćanja"

#. module: account
#: model:ir.actions.act_window,name:account.action_payment_term_form
#: model:ir.model,name:account.model_account_payment_term
#: model:ir.model.fields,field_description:account.field_account_invoice__payment_term_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report__payment_term_id
#: model:ir.model.fields,field_description:account.field_account_payment_term__name
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__payment_id
#: model:ir.ui.menu,name:account.menu_action_payment_term_form
#: model_terms:ir.ui.view,arch_db:account.view_partner_property_form
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_form
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_line_form
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_line_tree
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_search
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_tree
msgid "Payment Terms"
msgstr "Uslovi plaćanja"

#. module: account
#: model:ir.model,name:account.model_account_payment_term_line
msgid "Payment Terms Line"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment__payment_type
#: model:ir.model.fields,field_description:account.field_account_payment__payment_type
#: model:ir.model.fields,field_description:account.field_account_payment_method__payment_type
#: model:ir.model.fields,field_description:account.field_account_register_payments__payment_type
msgid "Payment Type"
msgstr "Tip plaćanja"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_form
msgid "Payment term explanation for the customer..."
msgstr "Opis uslova plaćanja za kupca..."

#. module: account
#: model:account.payment.term,note:account.account_payment_term_15days
msgid "Payment terms: 15 Days"
msgstr "Uslovi plaćanja: 15 dana"

#. module: account
#: model:account.payment.term,note:account.account_payment_term_2months
msgid "Payment terms: 2 Months"
msgstr ""

#. module: account
#: model:account.payment.term,note:account.account_payment_term_net
msgid "Payment terms: 30 Net Days"
msgstr "Uslovi plaćanja: 30 dana"

#. module: account
#: model:account.payment.term,note:account.account_payment_term_advance
msgid "Payment terms: 30% Advance End of Following Month"
msgstr "Uslovi plaćanja: 30% avansno krejem pretećeg mjeseca"

#. module: account
#: model:account.payment.term,note:account.account_payment_term_45days
msgid "Payment terms: 45 Days"
msgstr ""

#. module: account
#: model:account.payment.term,note:account.account_payment_term
msgid "Payment terms: End of Following Month"
msgstr "Uslovi plaćanja: Krajem pratećeg mjeseca"

#. module: account
#: model:account.payment.term,note:account.account_payment_term_immediate
msgid "Payment terms: Immediate Payment"
msgstr "Uslovi plaćanja: Avansno plaćanje"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__payment_id
msgid "Payment that created this entry"
msgstr "Plaćanje koje je kreiralo ovaj zapis"

#. module: account
#: code:addons/account/models/account_payment.py:370
#: model:ir.actions.act_window,name:account.action_account_payments
#: model:ir.actions.act_window,name:account.action_account_payments_payable
#: model:ir.model,name:account.model_account_payment
#: model:ir.model.fields,field_description:account.field_account_invoice__payment_ids
#: model:ir.ui.menu,name:account.menu_action_account_payments_payable
#: model:ir.ui.menu,name:account.menu_action_account_payments_receivable
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
#, python-format
msgid "Payments"
msgstr "Plaćanja"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Payments Matching"
msgstr "Usklađena plaćanja"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__payments_widget
msgid "Payments Widget"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_payments
#: model_terms:ir.actions.act_window,help:account.action_account_payments_payable
#: model_terms:ir.actions.act_window,help:account.action_account_payments_transfer
msgid ""
"Payments are used to register liquidity movements. You can process those "
"payments by your own means or by using installed facilities."
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_invoice_tree_pending_invoice
msgid "Pending Invoice"
msgstr "Faktura na čekanju"

#. module: account
#: selection:account.payment.term.line,value:0
msgid "Percent"
msgstr "Procenat"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__matched_percentage
msgid "Percentage Matched"
msgstr "Usklađeni procenat"

#. module: account
#: selection:account.tax,amount_type:0
#: selection:account.tax.template,amount_type:0
msgid "Percentage of Price"
msgstr "Procenat od cijene"

#. module: account
#: selection:account.tax,amount_type:0
#: selection:account.tax.template,amount_type:0
msgid "Percentage of Price Tax Included"
msgstr "Procenat od cijene sa porezom"

#. module: account
#: selection:account.reconcile.model,second_amount_type:0
#: selection:account.reconcile.model.template,second_amount_type:0
msgid "Percentage of amount"
msgstr "Procenat iznosa"

#. module: account
#: selection:account.reconcile.model,amount_type:0
#: selection:account.reconcile.model.template,amount_type:0
msgid "Percentage of balance"
msgstr "Procenat od salda"

#. module: account
#: code:addons/account/models/account_invoice.py:1975
#, python-format
msgid "Percentages on the Payment Terms lines must be between 0 and 100."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Period"
msgstr "Period"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_plaid
msgid "Plaid Connector"
msgstr ""

#. module: account
#: selection:account.invoice,activity_state:0
msgid "Planned"
msgstr "Planiran"

#. module: account
#: code:addons/account/models/account_invoice.py:1172
#, python-format
msgid "Please add at least one invoice line."
msgstr ""

#. module: account
#: code:addons/account/wizard/pos_box.py:27
#, python-format
msgid "Please check that the field 'Journal' is set on the Bank Statement"
msgstr "Molimo provjerite da je polje 'dnevnik' postavljeno na izvodu"

#. module: account
#: code:addons/account/wizard/pos_box.py:29
#, python-format
msgid "Please check that the field 'Transfer Account' is set on the company."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:301
#, python-format
msgid "Please define a sequence for the credit notes"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:306
#, python-format
msgid "Please define a sequence on the journal."
msgstr "Molimo definišite sekvencu na dnevniku."

#. module: account
#: code:addons/account/models/account_invoice.py:1170
#, python-format
msgid "Please define sequence on the journal related to this invoice."
msgstr "Molimo definišite sekvencu dnevnika povezanog sa ovom fakturom."

#. module: account
#: code:addons/account/models/company.py:340
#, python-format
msgid ""
"Please install a chart of accounts or create a miscellaneous journal before "
"proceeding."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "Please use the following communication for your payment :"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_form
msgid "Plus Bank"
msgstr "Plus bankovni računi"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_form
msgid "Plus Fixed Assets"
msgstr "Plus osnovna sredstva"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_form
msgid "Plus Net Profit"
msgstr "Plus neto dobit"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_form
msgid "Plus Non-Current Assets"
msgstr "Plus stalna sredstva"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_form
msgid "Plus Other Income"
msgstr "Plus ostali prihodi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__access_url
msgid "Portal Access URL"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Post"
msgstr "Proknjiži"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Post All Entries"
msgstr "Proknjiži sve zapise"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__post_at_bank_rec
msgid "Post At Bank Reconciliation"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_from_invoices
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_invoice_form
msgid "Post Difference In"
msgstr "Prokniži razliku u"

#. module: account
#: model:ir.actions.act_window,name:account.action_validate_account_move
#: model_terms:ir.ui.view,arch_db:account.validate_account_move_view
msgid "Post Journal Entries"
msgstr "Proknjiži zapise dnevnika"

#. module: account
#: selection:account.move,state:0 selection:account.payment,state:0
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Posted"
msgstr "Proknjižen"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Posted Journal Entries"
msgstr "Proknjiženi dnevnički zapisi"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Posted Journal Items"
msgstr "Proknjižene stavke dnevnika"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__bank_account_code_prefix
#: model:ir.model.fields,field_description:account.field_res_company__bank_account_code_prefix
msgid "Prefix of the bank accounts"
msgstr "Prefix računa banke"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__cash_account_code_prefix
msgid "Prefix of the cash accounts"
msgstr "Prefix računa gotovine"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__cash_account_code_prefix
msgid "Prefix of the main cash accounts"
msgstr "Prefix glavnih računa gotovine"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__transfer_account_code_prefix
msgid "Prefix of the main transfer accounts"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__transfer_account_code_prefix
msgid "Prefix of the transfer accounts"
msgstr ""

#. module: account
#: model:account.account.type,name:account.data_account_type_prepayments
#: model_terms:ir.ui.view,arch_db:account.view_account_form
msgid "Prepayments"
msgstr "Avansne uplate"

#. module: account
#: model:ir.model,name:account.model_account_reconcile_model
msgid ""
"Preset to create journal entries during a invoices and payments matching"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_form
msgid "Preview"
msgstr "Pregled"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
msgid "Preview as a PDF"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_form
msgid "Price"
msgstr "Cijena"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__is_print
#: model:ir.model.fields,field_description:account.field_res_config_settings__invoice_is_print
#: model_terms:ir.ui.view,arch_db:account.account_common_report_view
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
#: model_terms:ir.ui.view,arch_db:account.portal_invoice_page
msgid "Print"
msgstr "Ispis"

#. module: account
#: model:ir.model.fields,help:account.field_account_common_journal_report__amount_currency
#: model:ir.model.fields,help:account.field_account_print_journal__amount_currency
msgid ""
"Print Report with the currency column if the currency differs from the "
"company currency."
msgstr ""
"Ispiši izvještaj sa kolonom valute, ako je valuta različita od valute "
"kompanije."

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__invoice_is_print
msgid "Print by default"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Print checks to pay your vendors"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_product_product
#: model:ir.model.fields,field_description:account.field_account_analytic_line__product_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line__product_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report__product_id
#: model:ir.model.fields,field_description:account.field_account_move_line__product_id
#: model_terms:ir.ui.view,arch_db:account.view_account_analytic_line_filter_inherit_account
msgid "Product"
msgstr "Proizvod"

#. module: account
#: model:ir.model,name:account.model_product_category
#: model:ir.model.fields,field_description:account.field_account_invoice_report__categ_id
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Product Category"
msgstr "Kategorija proizvoda"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line__product_image
msgid "Product Image"
msgstr "Slika proizvoda"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__product_qty
msgid "Product Quantity"
msgstr "Količina proizvoda"

#. module: account
#: model:ir.model,name:account.model_product_template
msgid "Product Template"
msgstr "Predlog proizvoda"

#. module: account
#: model:ir.actions.act_window,name:account.product_product_action
#: model:ir.actions.act_window,name:account.product_product_action_purchasable
#: model:ir.actions.act_window,name:account.product_product_action_sellable
#: model:ir.ui.menu,name:account.product_product_menu_purchasable
#: model:ir.ui.menu,name:account.product_product_menu_sellable
#: model_terms:ir.ui.view,arch_db:account.product_product_view_tree
msgid "Products"
msgstr "Proizvodi"

#. module: account
#: code:addons/account/models/account_bank_statement.py:179
#, python-format
msgid "Profit"
msgstr "Dobit"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_form
msgid "Profit &amp; Loss"
msgstr "Dobitak i Gubitak"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__profit_account_id
msgid "Profit Account"
msgstr "Konto dobiti"

#. module: account
#: code:addons/account/models/account_reconcile_model.py:378
#, python-format
msgid ""
"Programmation Error: Can't call _get_invoice_matching_query() for different "
"rules than 'invoice_matching'"
msgstr ""

#. module: account
#: code:addons/account/models/account_reconcile_model.py:489
#, python-format
msgid ""
"Programmation Error: Can't call _get_wo_suggestion_query() for different "
"rules than 'writeoff_suggestion'"
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:282
#, python-format
msgid ""
"Programming error: wizard action executed without active_ids in context."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_chart_template_form
msgid "Properties"
msgstr "Svojstva"

#. module: account
#: selection:account.journal,type:0
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_search
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_template_search
msgid "Purchase"
msgstr "Nabava"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Purchase Representative"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Purchase Tax"
msgstr "Porez nabave"

#. module: account
#: selection:account.tax,type_tax_use:0
#: selection:account.tax.template,type_tax_use:0
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_search
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Purchases"
msgstr "Nabavke"

#. module: account
#: model:ir.actions.act_window,name:account.action_cash_box_in
#: model_terms:ir.ui.view,arch_db:account.cash_box_in_form
msgid "Put Money In"
msgstr "Stavi novac unutra"

#. module: account
#: selection:account.tax,amount_type:0
#: selection:account.tax.template,amount_type:0
msgid "Python Code"
msgstr "Python Kod"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "QIF Import"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line__quantity
#: model:ir.model.fields,field_description:account.field_account_move_line__quantity
msgid "Quantity"
msgstr "Količina"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_form
msgid "Quantity:"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__rating_value
msgid "Rating Value"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_refund__description
#: model:ir.model.fields,field_description:account.field_cash_box_in__name
#: model:ir.model.fields,field_description:account.field_cash_box_out__name
msgid "Reason"
msgstr "Razlog"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.tax_adjustments_wizard
msgid "Reason..."
msgstr "Razlog..."

#. module: account
#: model:account.account.type,name:account.data_account_type_receivable
#: selection:account.account.type,type:0
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Receivable"
msgstr "Dugovni saldo-konto"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__property_account_receivable_id
#: model_terms:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
msgid "Receivable Account"
msgstr "Dugovni saldo-konto"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_form
#: model_terms:ir.ui.view,arch_db:account.view_account_search
msgid "Receivable Accounts"
msgstr "Dugovna salda-konta"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__account_id
msgid "Receivable/Payable Account"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.product_template_form_view
msgid "Receivables"
msgstr ""

#. module: account
#: selection:account.abstract.payment,payment_type:0
#: selection:account.payment,payment_type:0
#: selection:account.register.payments,payment_type:0
msgid "Receive Money"
msgstr "Primi novac"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment__partner_bank_account_id
#: model:ir.model.fields,field_description:account.field_account_payment__partner_bank_account_id
#: model:ir.model.fields,field_description:account.field_account_register_payments__partner_bank_account_id
msgid "Recipient Bank Account"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
msgid "Recipients"
msgstr "Primaoci"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__recompute_tax_line
msgid "Recompute Tax Line"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:79
#: code:addons/account/static/src/xml/account_reconciliation.xml:111
#: code:addons/account/static/src/xml/account_reconciliation.xml:112
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_form
#, python-format
msgid "Reconcile"
msgstr "Zatvori knjiženja"

#. module: account
#: model:ir.actions.client,name:account.action_view_account_move_line_reconcile
msgid "Reconcile Entries"
msgstr "Zatvori zapise"

#. module: account
#: model:ir.model,name:account.model_account_reconcile_model_template
msgid "Reconcile Model Template"
msgstr ""

#. module: account
#: selection:account.payment,state:0
#: model:ir.model.fields,field_description:account.field_account_move_line__reconciled
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Reconciled"
msgstr "Zatvoreno"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__reconciled_invoice_ids
msgid "Reconciled Invoices"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Reconciled entries"
msgstr "Usklađene stavke"

#. module: account
#: model:ir.actions.client,name:account.action_manual_reconciliation
#: model:ir.ui.menu,name:account.menu_action_manual_reconciliation
msgid "Reconciliation"
msgstr "Zatvaranje"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_reconcile_model
#: model:ir.ui.menu,name:account.action_account_reconcile_model_menu
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Reconciliation Models"
msgstr "Modeli zatvaranja"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_full_reconcile__partial_reconcile_ids
msgid "Reconciliation Parts"
msgstr "Dijelovi zatvaranja"

#. module: account
#: model:ir.actions.client,name:account.action_bank_reconcile
#: model:ir.actions.client,name:account.action_bank_reconcile_bank_statements
msgid "Reconciliation on Bank Statements"
msgstr "Zatvaranje na izvodima iz banke"

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_vendor_bill_template
msgid "Record a new vendor bill"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Record transactions in foreign currencies"
msgstr ""

#. module: account
#: code:addons/account/models/account.py:930
#, python-format
msgid "Recursion found for tax '%s'."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:285
#, python-format
msgid "Ref"
msgstr "Ref"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_analytic_line__ref
msgid "Ref."
msgstr "Ref."

#. module: account
#: code:addons/account/controllers/portal.py:42
#: model:ir.model.fields,field_description:account.field_account_bank_statement__name
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__ref
#: model:ir.model.fields,field_description:account.field_account_move__ref
#: model:ir.model.fields,field_description:account.field_account_move_line__ref
#: model:ir.model.fields,field_description:account.field_cash_box_in__ref
#, python-format
msgid "Reference"
msgstr "Referenca"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__uom_name
msgid "Reference Unit of Measure"
msgstr "Referentna jedinica mjere"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__origin
#: model:ir.model.fields,help:account.field_account_invoice_line__origin
msgid "Reference of the document that produced this invoice."
msgstr "Referenca dokumenta koji je stvorio ovu fakturu."

#. module: account
#: model:ir.model.fields,help:account.field_account_payment__payment_reference
msgid ""
"Reference of the document used to issue this payment. Eg. check number, file"
" name, etc."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__name
msgid "Reference/Description"
msgstr "Referenca/Opis"

#. module: account
#: model:ir.actions.act_window,name:account.action_invoice_in_refund
#: model:ir.ui.menu,name:account.menu_action_invoice_in_refund
msgid "Refund"
msgstr "Refundacija"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__refund_invoice_ids
msgid "Refund Invoices"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_account_invoice_payment
#: model:ir.actions.act_window,name:account.action_account_payment_from_invoices
#: model_terms:ir.ui.view,arch_db:account.invoice_form
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_form
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_form
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_from_invoices
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_invoice_form
msgid "Register Payment"
msgstr "Registracija uplate"

#. module: account
#: model:ir.model,name:account.model_account_register_payments
msgid "Register Payments"
msgstr "Registracija uplate"

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_bank_statement_tree
msgid "Register a bank statement"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_payments
#: model_terms:ir.actions.act_window,help:account.action_account_payments_payable
#: model_terms:ir.actions.act_window,help:account.action_account_payments_transfer
msgid "Register a payment"
msgstr ""

#. module: account
#: selection:account.account.type,type:0
msgid "Regular"
msgstr "Regularni"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__res_id
msgid "Related Document ID"
msgstr "Povezani ID dokumenta"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__model
msgid "Related Document Model"
msgstr "Povezani model dokumenta"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__rating_ids
msgid "Related ratings"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__residual_company_signed
msgid "Remaining amount due in the currency of the company."
msgstr "Preostali iznos duga u valuti kompanije."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__residual_signed
msgid "Remaining amount due in the currency of the invoice."
msgstr "Preostali iznos duga u valuti fakture."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__residual
msgid "Remaining amount due."
msgstr "Preostali iznos duga."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template__tax_dest_id
msgid "Replacement Tax"
msgstr "Zamjenski porez"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__reply_to
msgid ""
"Reply email address. Setting the reply_to bypasses the automatic thread "
"creation."
msgstr ""
"Email adresa odgovora. Postavljanjem reply_to zaobilazi automatsko kreiranje"
" niti."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__reply_to
msgid "Reply-To"
msgstr "Odgovori na"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_common_report_view
msgid "Report Options"
msgstr "Postavke izvješća"

#. module: account
#: model:ir.ui.menu,name:account.account_report_folder
#: model:ir.ui.menu,name:account.menu_finance_reports
msgid "Reporting"
msgstr "Izvještavanje"

#. module: account
#: model:ir.model.fields,help:account.field_account_cash_rounding__rounding
msgid "Represent the non-zero value smallest coinage (for example, 0.05)."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__res_partner_bank_id
msgid "Res Partner Bank"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_form
msgid "Reset to Draft"
msgstr "Vrati u pripremu"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:290
#, python-format
msgid "Residual"
msgstr "Ostatak"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__amount_residual
msgid "Residual Amount"
msgstr "Preostali iznos"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__amount_residual_currency
msgid "Residual Amount in Currency"
msgstr "Preostali iznos u valuti"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:32
#, python-format
msgid "Residual amount"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__user_id
msgid "Responsible"
msgstr "Odgovoran"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__activity_user_id
msgid "Responsible User"
msgstr "Odgovorni korisnik"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_partner_category_ids
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_partner_category_ids
msgid "Restrict Partner Categories to"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_partner_ids
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_partner_ids
msgid "Restrict Partners to"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__match_same_currency
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__match_same_currency
msgid ""
"Restrict to propositions having the same currency as the statement line."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_digest_digest__kpi_account_total_revenue
msgid "Revenue"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_deferred_revenue
msgid "Revenue Recognition"
msgstr "Priznavanje dobiti"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__account_line_id
msgid "Revenue/Expense Account"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__reverse_date
msgid "Reversal Date"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_reversal__date
msgid "Reversal date"
msgstr "Datum povrata"

#. module: account
#: code:addons/account/models/account_move.py:399
#, python-format
msgid "Reversal of: %s"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__auto_reverse
msgid "Reverse Automatically"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__reverse_entry_id
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Reverse Entry"
msgstr "Povrat zapisa"

#. module: account
#: code:addons/account/wizard/account_move_reversal.py:20
#: model:ir.actions.act_window,name:account.action_view_account_move_reversal
#: model_terms:ir.ui.view,arch_db:account.view_account_move_reversal
#, python-format
msgid "Reverse Moves"
msgstr "Povrat knjiženja"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Reversed entry"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_chart_of_account_step
msgid "Review"
msgstr ""

#. module: account
#: selection:res.company,tax_calculation_rounding_method:0
msgid "Round Globally"
msgstr "Zaokruživanje globalno"

#. module: account
#: selection:res.company,tax_calculation_rounding_method:0
msgid "Round per Line"
msgstr "Zaokruživanje po stavci"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.rounding_form_view
msgid "Rounding Form"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line__is_rounding_line
msgid "Rounding Line"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__rounding_method
msgid "Rounding Method"
msgstr "Način zaokruživanja"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__rounding
msgid "Rounding Precision"
msgstr "Preciznost zaokruživanja"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__strategy
msgid "Rounding Strategy"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.rounding_tree_view
msgid "Rounding Tree"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:134
#, python-format
msgid "Run"
msgstr "Pokrenut"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_sepa
msgid "SEPA Credit Transfer (SCT)"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "SEPA Direct Debit (SDD)"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "SEPA QR Code"
msgstr ""

#. module: account
#: selection:account.journal,type:0
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_search
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_template_search
msgid "Sale"
msgstr "Prodaja"

#. module: account
#: selection:account.tax,type_tax_use:0
#: selection:account.tax.template,type_tax_use:0
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_search
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Sales"
msgstr "Prodaja"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_onboarding_sale_tax_form
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Sales Tax"
msgstr "Prodajni porez"

#. module: account
#: model:ir.actions.act_window,name:account.action_open_account_onboarding_sale_tax
msgid "Sales tax"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__user_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report__user_id
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Salesperson"
msgstr "Prodavač(ica)"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_same_currency
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_same_currency
msgid "Same Currency Matching"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_sample_invoice_step
msgid "Sample Invoice"
msgstr ""

#. module: account
#: code:addons/account/models/company.py:462
#, python-format
msgid "Sample invoice"
msgstr ""

#. module: account
#: code:addons/account/models/company.py:468
#, python-format
msgid "Sample invoice line name"
msgstr ""

#. module: account
#: code:addons/account/models/company.py:475
#, python-format
msgid "Sample invoice line name 2"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_sample_invoice_step
msgid "Sample invoice sent!"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__sanitized_acc_number
msgid "Sanitized Account Number"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:218
#, python-format
msgid "Save and New"
msgstr "Sačuvaj i Novi"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
msgid "Save as a new template"
msgstr "Spremi kao novi predložak"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
msgid "Save as new template"
msgstr "Spremi kao novi predložak"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Save this page and come back here to set up the feature."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_search
msgid "Search Account Journal"
msgstr "Pretraga dnevnika knjiženja"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_template_search
msgid "Search Account Templates"
msgstr "Pretraga prijedloga konta"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_search
msgid "Search Bank Statements"
msgstr "Pretraga izvoda iz banke"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_line_search
msgid "Search Bank Statements Line"
msgstr "Pretraga stavki izvoda iz banke"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
msgid "Search Chart of Account Templates"
msgstr "Pretraga predloga kontnog plana"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_position_filter
msgid "Search Fiscal Positions"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Search Invoice"
msgstr "Pretraga faktura"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Search Journal Items"
msgstr "Pretraži stavke dnevnika"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Search Move"
msgstr "Pretraga knjiženja"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_template_search
msgid "Search Tax Templates"
msgstr "Pretraži predloge poreza"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_tax_view_search
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Search Taxes"
msgstr "Pretraži poreze"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__second_account_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__second_account_id
msgid "Second Account"
msgstr "Drugi konto"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__second_amount_type
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__second_amount_type
msgid "Second Amount type"
msgstr "Drugi tip iznosa"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__second_analytic_account_id
msgid "Second Analytic Account"
msgstr "Drugi analitički konto"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__second_analytic_tag_ids
msgid "Second Analytic Tags"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__second_journal_id
msgid "Second Journal"
msgstr "Drugi dnevnik"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__second_label
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__second_label
msgid "Second Journal Item Label"
msgstr "Drugi naslov stavke dnevnika"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__second_tax_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__second_tax_id
msgid "Second Tax"
msgstr "Drugi porez"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__second_tax_amount_type
msgid "Second Tax Amount Type"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__force_second_tax_included
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__force_second_tax_included
msgid "Second Tax Included in Price"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__second_amount
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__second_amount
msgid "Second Write-off Amount"
msgstr ""

#. module: account
#: selection:account.invoice.line,display_type:0
#: model_terms:ir.ui.view,arch_db:account.view_invoice_line_form
msgid "Section"
msgstr "Odjel"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__access_token
msgid "Security Token"
msgstr "Sigurnosni token"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__type
msgid ""
"Select 'Sale' for customer invoices journals.\n"
"Select 'Purchase' for vendor bills journals.\n"
"Select 'Cash' or 'Bank' for journals that are used in customer or vendor payments.\n"
"Select 'General' for miscellaneous operations journals."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:80
#, python-format
msgid "Select a partner or choose a counterpart"
msgstr "Odaberi partnera ili drugu protustavku"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Select an old vendor bill"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_term_line__value
msgid "Select here the kind of valuation related to this payment terms line."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Select this if the taxes should use cash basis, which will create an entry "
"for such taxes on a given account during reconciliation."
msgstr ""

#. module: account
#: code:addons/account/wizard/account_invoice_state.py:21
#, python-format
msgid ""
"Selected invoice(s) cannot be confirmed as they are not in 'Draft' state."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_partner__invoice_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""
"Odabirom opcije \"Upozorenje\" obavijestit ćete korisnika porukom, odabirom "
"opcije \"Blokiranje poruke\" učinit ćete izuzetak s porukom i blokirati tok."
" Poruku treba napisati u slijedećem polju."

#. module: account
#: model:ir.actions.act_window,name:account.invoice_send
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
msgid "Send"
msgstr "Pošalji"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
#: model_terms:ir.ui.view,arch_db:account.invoice_form
msgid "Send & Print"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__invoice_is_email
msgid "Send Email"
msgstr "Pošalji e-mail"

#. module: account
#: code:addons/account/models/account_invoice.py:607
#, python-format
msgid "Send Invoice"
msgstr ""

#. module: account
#: selection:account.abstract.payment,payment_type:0
#: selection:account.payment,payment_type:0
#: selection:account.register.payments,payment_type:0
msgid "Send Money"
msgstr "Pošalji novac"

#. module: account
#: model:ir.actions.act_window,name:account.account_send_payment_receipt_by_email_action
msgid "Send Receipt By Email"
msgstr "Pošalji račun email-om"

#. module: account
#: model:ir.actions.act_window,name:account.action_open_account_onboarding_sample_invoice
msgid "Send a sample invoice"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_sample_invoice_step
msgid "Send an invoice to test the customer portal."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_sample_invoice_step
msgid "Send sample"
msgstr ""

#. module: account
#: selection:account.payment,state:0
#: model:ir.model.fields,field_description:account.field_account_invoice__sent
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Sent"
msgstr "Poslano"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "September"
msgstr "Septembar"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__sequence
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__sequence
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__sequence
#: model:ir.model.fields,field_description:account.field_account_invoice_line__sequence
#: model:ir.model.fields,field_description:account.field_account_invoice_tax__sequence
#: model:ir.model.fields,field_description:account.field_account_journal__sequence
#: model:ir.model.fields,field_description:account.field_account_payment_term__sequence
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__sequence
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__sequence
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__sequence
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__sequence
#: model:ir.model.fields,field_description:account.field_account_tax__sequence
#: model:ir.model.fields,field_description:account.field_account_tax_group__sequence
#: model:ir.model.fields,field_description:account.field_account_tax_template__sequence
msgid "Sequence"
msgstr "Sekvenca"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:239
#, python-format
msgid "Set"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_form
msgid "Set To Draft"
msgstr "Stavi u pripremu"

#. module: account
#: model:ir.model.fields,help:account.field_account_account_tag__active
msgid "Set active to false to hide the Account Tag without removing it."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__active
msgid "Set active to false to hide the Journal without removing it."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__active
#: model:ir.model.fields,help:account.field_account_tax_template__active
msgid "Set active to false to hide the tax without removing it."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_sale_tax_step
msgid "Set taxes"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_chart_template__visible
msgid ""
"Set this to False if you don't want this template to be used actively in the"
" wizard that generate Chart of Accounts from templates, this is useful when "
"you want to generate accounts of this template only when loading its child "
"template."
msgstr ""
"Isključite ovo ako ne želite da se ovaj predložak aktivno koristi u "
"čarobnjaku koji generiše kontni plan iz predložaka. Ovo je vrlo korisno kada"
" želite generisati konta ovog predloška samo iz podređenog predloška."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Set to Draft"
msgstr "Postavi u pripremu"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:85
#: code:addons/account/static/src/xml/account_reconciliation.xml:124
#: model:ir.actions.act_window,name:account.action_account_config
#: model:ir.actions.act_window,name:account.action_open_settings
#: model:ir.ui.menu,name:account.menu_account_config
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#, python-format
msgid "Settings"
msgstr "Postavke"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Setup"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_bank_account_step
msgid "Setup your bank account to sync bank feeds."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_chart_of_account_step
msgid "Setup your chart of accounts and record initial balances."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.dashboard_onboarding_company_step
msgid "Setup your company's data for reports headers."
msgstr ""

#. module: account
#: model:ir.actions.server,name:account.model_account_invoice_action_share
msgid "Share"
msgstr "Podijeli"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__code
msgid "Short Code"
msgstr "Kratka šifra"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_register_payments__show_communication_field
msgid "Show Communication Field"
msgstr ""

#. module: account
#: model:res.groups,name:account.group_account_user
msgid "Show Full Accounting Features"
msgstr "Prikaži pune računovodstvene mogućnosti"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment__show_partner_bank_account
#: model:ir.model.fields,field_description:account.field_account_payment__show_partner_bank_account
#: model:ir.model.fields,field_description:account.field_account_register_payments__show_partner_bank_account
msgid "Show Partner Bank Account"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Show active taxes"
msgstr "Prikaži aktivne poreze"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Show all records which has next action date is before today"
msgstr "Prikaži sve zapise koji imaju datum sljedeće akcije prije danas"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Show inactive taxes"
msgstr "Prikaži neaktivne poreze"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__show_on_dashboard
msgid "Show journal on dashboard"
msgstr "Prikaži dnevnik na kontrolnoj ploči"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__group_show_line_subtotals_tax_included
msgid "Show line subtotals with taxes (B2C)"
msgstr ""

#. module: account
#: model:res.groups,comment:account.group_show_line_subtotals_tax_included
msgid "Show line subtotals with taxes included (B2C)"
msgstr "Prikaži cijene sa uključenim PDV-om (B2C)"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__group_show_line_subtotals_tax_excluded
#: model:res.groups,comment:account.group_show_line_subtotals_tax_excluded
msgid "Show line subtotals without taxes (B2B)"
msgstr "Prikaži cijene bez uključenog PDV-a  (B2B)"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:113
#, python-format
msgid "Skip"
msgstr "Preskoči"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__author_avatar
msgid ""
"Small-sized image of this contact. It is automatically resized as a 64x64px "
"image, with aspect ratio preserved. Use this field anywhere a small image is"
" required."
msgstr ""
"Mala slika ovog kontakta. Veličina je automatski promijenjena na 64x64 px "
"sliku. sa očuvanim proporcijama. Koristite ovo polje gdje god je potrebna "
"mala slika."

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:764
#, python-format
msgid "Some fields are undefined"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_setup_bank_manual_config__bank_bic
msgid "Sometimes called BIC or Swift."
msgstr "Ponekad se naziva BIC ili SWIFT"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__origin
#: model:ir.model.fields,field_description:account.field_account_invoice_line__origin
msgid "Source Document"
msgstr "Izvorni dokument"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__source_email
msgid "Source Email"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_cash_rounding__strategy
msgid ""
"Specify which way will be used to round the invoice amount to the rounding "
"precision"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__starred
msgid "Starred"
msgstr "Sa zvjezdicom"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__date_from
#: model:ir.model.fields,field_description:account.field_account_common_report__date_from
#: model:ir.model.fields,field_description:account.field_account_fiscal_year__date_from
#: model:ir.model.fields,field_description:account.field_account_print_journal__date_from
msgid "Start Date"
msgstr "Datum početka"

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_year__date_from
msgid "Start Date, included in the fiscal year."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__balance_start
msgid "Starting Balance"
msgstr "Početni saldo"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__cashbox_start_id
msgid "Starting Cashbox"
msgstr "Početno stanje blagajne"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "State"
msgstr "Rep./Fed."

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_dashboard_onboarding_state
msgid "State of the account dashboard onboarding panel"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_invoice_onboarding_state
msgid "State of the account invoice onboarding panel"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_setup_bank_data_state
msgid "State of the onboarding bank data step"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_setup_coa_state
msgid "State of the onboarding charts of account step"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_setup_fy_data_state
msgid "State of the onboarding fiscal year step"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_onboarding_invoice_layout_state
msgid "State of the onboarding invoice layout step"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_onboarding_sale_tax_state
msgid "State of the onboarding sale tax step"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_onboarding_sample_invoice_state
msgid "State of the onboarding sample invoice step"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__parent_state
msgid "State of the parent account.move"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__statement_id
#: model:ir.model.fields,field_description:account.field_account_move_line__statement_id
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Statement"
msgstr "Izvod"

#. module: account
#: code:addons/account/models/account_bank_statement.py:248
#, python-format
msgid "Statement %s confirmed, journal items were created."
msgstr "Izvod %s potvrđen, dnevnički zapisi su kreirani."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_line_form
msgid "Statement Line"
msgstr "Stavke izvoda"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__line_ids
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_form
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_line_tree
msgid "Statement lines"
msgstr "Stavke izvoda"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_tree
msgid "Statements"
msgstr "Izvodi"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
msgid "States"
msgstr "Status"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__states_count
msgid "States Count"
msgstr ""

#. module: account
#: code:addons/account/controllers/portal.py:43
#: model:ir.model.fields,field_description:account.field_account_bank_statement__state
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__state
#: model:ir.model.fields,field_description:account.field_account_invoice__state
#: model:ir.model.fields,field_description:account.field_account_move__state
#: model:ir.model.fields,field_description:account.field_account_payment__state
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_search
#, python-format
msgid "Status"
msgstr "Status"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_bank_account_step
#: model_terms:ir.ui.view,arch_db:account.onboarding_fiscal_year_step
#: model_terms:ir.ui.view,arch_db:account.onboarding_sale_tax_step
msgid "Step Completed!"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__subject
msgid "Subject"
msgstr "Tema"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
msgid "Subject..."
msgstr "Naslov..."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cashbox_line__subtotal
#: model_terms:ir.ui.view,arch_db:account.invoice_form
msgid "Subtotal"
msgstr "Podukupno"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__subtype_id
msgid "Subtype"
msgstr "Podtip"

#. module: account
#: selection:account.reconcile.model,rule_type:0
#: selection:account.reconcile.model.template,rule_type:0
#: code:addons/account/models/account_reconcile_model.py:20
#: code:addons/account/models/chart_template.py:933
#, python-format
msgid "Suggest counterpart values."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Supplier Payments"
msgstr "Plaćanja dobavljaču"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_form
msgid "TOTAL ASSETS"
msgstr "UKUPNO SREDSTVA"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_form
msgid "TOTAL EQUITY"
msgstr "UKUPNO KAPITAL"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__tag_ids
#: model:ir.model.fields,field_description:account.field_account_tax__tag_ids
#: model_terms:ir.ui.view,arch_db:account.account_tag_view_form
msgid "Tags"
msgstr "Oznake"

#. module: account
#: model:ir.actions.act_window,name:account.action_cash_box_out
#: model_terms:ir.ui.view,arch_db:account.cash_box_out_form
msgid "Take Money Out"
msgstr "Izdavanje novca"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__target_move
#: model:ir.model.fields,field_description:account.field_account_common_report__target_move
#: model:ir.model.fields,field_description:account.field_account_print_journal__target_move
msgid "Target Moves"
msgstr "Cilj prijenosa"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:185
#: model:ir.model,name:account.model_account_tax
#: model:ir.model.fields,field_description:account.field_account_invoice__amount_tax
#: model:ir.model.fields,field_description:account.field_account_invoice_tax__tax_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__tax_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__tax_id
#: model_terms:ir.ui.view,arch_db:account.account_tax_view_search
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_search
#, python-format
msgid "Tax"
msgstr "Porez"

#. module: account
#: code:addons/account/models/chart_template.py:279
#: code:addons/account/models/chart_template.py:281
#, python-format
msgid "Tax %.2f%%"
msgstr "Porez %.2f%%"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_tax__account_id
#: model:ir.model.fields,field_description:account.field_account_tax__account_id
#: model:ir.model.fields,field_description:account.field_account_tax_template__account_id
msgid "Tax Account"
msgstr "Konto poreza"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__refund_account_id
msgid "Tax Account on Credit Notes"
msgstr "Konto poreza na knjižnim odobrenjima"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_template__refund_account_id
msgid "Tax Account on Refunds"
msgstr "Konto poreza na povratima"

#. module: account
#: model:ir.actions.act_window,name:account.tax_adjustments_form
#: model:ir.ui.menu,name:account.menu_action_tax_adjustment
msgid "Tax Adjustments"
msgstr "Ispravke poreza"

#. module: account
#: model:ir.model,name:account.model_tax_adjustments_wizard
msgid "Tax Adjustments Wizard"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line__price_tax
#: model:ir.model.fields,field_description:account.field_account_invoice_tax__amount
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "Tax Amount"
msgstr "Iznos poreza"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__tax_amount_type
msgid "Tax Amount Type"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Tax Application"
msgstr "Primjena poreza"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__tax_calculation_rounding_method
msgid "Tax Calculation Rounding Method"
msgstr "Metada zaokruživanje izračunavanja poreza"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__tax_cash_basis_rec_id
msgid "Tax Cash Basis Entry of"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__tax_cash_basis_journal_id
msgid "Tax Cash Basis Journal"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__amount_type
#: model:ir.model.fields,field_description:account.field_account_tax_template__amount_type
msgid "Tax Computation"
msgstr "Izračunavanje poreza"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "Tax Declaration"
msgstr "Porezni iskaz"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_tax__name
msgid "Tax Description"
msgstr "Opis poreza"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__tax_exigibility
#: model:ir.model.fields,field_description:account.field_account_tax_template__tax_exigibility
msgid "Tax Due"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_tree
#: model_terms:ir.ui.view,arch_db:account.invoice_tree
msgid "Tax Excluded"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_tax_group
#: model:ir.model.fields,field_description:account.field_account_tax__tax_group_id
#: model:ir.model.fields,field_description:account.field_account_tax_template__tax_group_id
msgid "Tax Group"
msgstr "Grupa poreza"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:212
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__force_tax_included
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__force_tax_included
#, python-format
msgid "Tax Included in Price"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__tax_line_ids
msgid "Tax Lines"
msgstr "Stavke poreza"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__tax_ids
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__tax_ids
#: model_terms:ir.ui.view,arch_db:account.view_account_position_form
msgid "Tax Mapping"
msgstr "Mapiranje poreza"

#. module: account
#: model:ir.model,name:account.model_account_fiscal_position_tax_template
msgid "Tax Mapping Template of Fiscal Position"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_fiscal_position_tax
msgid "Tax Mapping of Fiscal Position"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__name
#: model:ir.model.fields,field_description:account.field_account_tax_template__name
msgid "Tax Name"
msgstr "Naziv poreza"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__cash_basis_account_id
#: model:ir.model.fields,field_description:account.field_account_tax_template__cash_basis_account_id
msgid "Tax Received Account"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__type_tax_use
#: model:ir.model.fields,field_description:account.field_account_tax_template__type_tax_use
msgid "Tax Scope"
msgstr "Područje poreza"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template__tax_src_id
msgid "Tax Source"
msgstr "Izvor poreza"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_template_search
msgid "Tax Template"
msgstr "Prijedlog poreza"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__tax_template_ids
msgid "Tax Template List"
msgstr "Lista prijedloga poreza"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_tax_template_form
msgid "Tax Templates"
msgstr "Prijedlozi poreza"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__tax_type_domain
msgid "Tax Type Domain"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__amount_by_group
msgid "Tax amount by group"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__tax_calculation_rounding_method
msgid "Tax calculation rounding method"
msgstr "Metoda zaokruživanja poreza"

#. module: account
#: model:res.groups,name:account.group_show_line_subtotals_tax_excluded
msgid "Tax display B2B"
msgstr "Prikaz PDV-a B2B"

#. module: account
#: model:res.groups,name:account.group_show_line_subtotals_tax_included
msgid "Tax display B2C"
msgstr "Prikaz PDV-a B2C"

#. module: account
#: sql_constraint:account.tax:0 sql_constraint:account.tax.template:0
msgid "Tax names must be unique !"
msgstr "Naziv poreza mora biti jedinstven !"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax__tax_src_id
msgid "Tax on Product"
msgstr "Porezi na proizvodu"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax__tax_dest_id
msgid "Tax to Apply"
msgstr "Porezi za primjeniti"

#. module: account
#: selection:res.config.settings,show_line_subtotals_tax_selection:0
msgid "Tax-Excluded"
msgstr ""

#. module: account
#: selection:res.config.settings,show_line_subtotals_tax_selection:0
msgid "Tax-Included"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "TaxCloud"
msgstr ""

#. module: account
#: selection:account.account.tag,applicability:0
#: model:account.tax.group,name:account.tax_group_taxes
#: model:ir.actions.act_window,name:account.action_tax_form
#: model:ir.model.fields,field_description:account.field_account_invoice_line__invoice_line_tax_ids
#: model:ir.model.fields,field_description:account.field_account_move_line__tax_ids
#: model:ir.ui.menu,name:account.menu_action_tax_form
#: model_terms:ir.ui.view,arch_db:account.invoice_form
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_form
#: model_terms:ir.ui.view,arch_db:account.onboarding_sale_tax_step
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
msgid "Taxes"
msgstr "Porezi"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Taxes Applied"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_position_template_form
msgid "Taxes Mapping"
msgstr "Mapiranje poreza"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_template_search
msgid "Taxes used in Purchases"
msgstr "Porezi korišćeni u kupovini"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_template_search
msgid "Taxes used in Sales"
msgstr "Porezi korišćeni u prodaji"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Taxes, fiscal positions, chart of accounts &amp; legal statements for your "
"country"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_payment__invoice_ids
msgid ""
"Technical field containing the invoices for which the payment has been generated.\n"
"                                                                                                                                                                       This does not especially correspond to the invoices reconciled with the payment,\n"
"                                                                                                                                                                       as it can have been generated first, and reconciled later"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line__display_type
msgid "Technical field for UX purpose."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__balance
msgid ""
"Technical field holding the debit - credit in order to open meaningful graph"
" views from reports"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__balance_cash_basis
msgid ""
"Technical field holding the debit_cash_basis - credit_cash_basis in order to"
" open meaningful graph views from reports"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__move_name
msgid ""
"Technical field holding the number given to the invoice, automatically set "
"when the invoice is validated then stored to set the same number again if "
"the invoice is cancelled, set to draft and re-validated."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line__move_name
#: model:ir.model.fields,help:account.field_account_payment__move_name
msgid ""
"Technical field holding the number given to the journal entry, automatically"
" set when the statement line is reconciled then stored to set the same "
"number again if the line is cancelled, set to draft and re-processed again."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_abstract_payment__multi
#: model:ir.model.fields,help:account.field_account_payment__multi
#: model:ir.model.fields,help:account.field_account_register_payments__multi
msgid ""
"Technical field indicating if the user selected invoices from multiple "
"partners or from different types."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_refund__refund_only
msgid ""
"Technical field to hide filter_refund in case invoice is partially paid"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__journal_type
#: model:ir.model.fields,help:account.field_account_payment__has_invoices
msgid "Technical field used for usability purposes"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move__matched_percentage
msgid "Technical field used in cash basis method"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__second_tax_amount_type
msgid ""
"Technical field used inside the view to make the force_second_tax_included "
"field invisible if the tax is a group."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__is_second_tax_price_included
msgid ""
"Technical field used inside the view to make the force_second_tax_included "
"field readonly if the tax is already price included."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__tax_amount_type
msgid ""
"Technical field used inside the view to make the force_tax_included field "
"invisible if the tax is a group."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__is_tax_price_included
msgid ""
"Technical field used inside the view to make the force_tax_included field "
"readonly if the tax is already price included."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_abstract_payment__payment_method_code
#: model:ir.model.fields,help:account.field_account_payment__payment_method_code
#: model:ir.model.fields,help:account.field_account_register_payments__payment_method_code
msgid ""
"Technical field used to adapt the interface to the payment type selected."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_partial_reconcile__max_date
msgid ""
"Technical field used to determine at which date this reconciliation needs to"
" be shown on the aged receivable/payable reports."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move__tax_type_domain
msgid "Technical field used to have a dynamic taxes domain on the form view."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_abstract_payment__hide_payment_method
#: model:ir.model.fields,help:account.field_account_payment__hide_payment_method
#: model:ir.model.fields,help:account.field_account_register_payments__hide_payment_method
msgid ""
"Technical field used to hide the payment method if the selected journal has "
"only one available which is 'manual'"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move__tax_cash_basis_rec_id
msgid ""
"Technical field used to keep track of the tax cash basis reconciliation. "
"This is needed when cancelling the source: it will post the inverse journal "
"entry to cancel that part too."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__recompute_tax_line
msgid ""
"Technical field used to know if the tax_ids field has been modified in the "
"UI."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_abstract_payment__show_partner_bank_account
#: model:ir.model.fields,help:account.field_account_payment__show_partner_bank_account
#: model:ir.model.fields,help:account.field_account_register_payments__show_partner_bank_account
msgid ""
"Technical field used to know whether the field `partner_bank_account_id` "
"needs to be displayed or not in the payments form views"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__tax_exigible
msgid ""
"Technical field used to mark a tax line as exigible in the vat report or not"
" (only exigible journal items are displayed). By default all new journal "
"items are directly exigible, but with the feature cash_basis on taxes, some "
"will become exigible only when the payment is recorded."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line__account_number
msgid ""
"Technical field used to store the bank account number before its creation, "
"upon the line's processing"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__tax_line_grouping_key
msgid ""
"Technical field used to store the old values of fields used to compute tax "
"lines (in account.move form view) between the moment the user changed it and"
" the moment the ORM reflects that change in its one2many"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__chart_template_id
msgid "Template"
msgstr "Prijedlog"

#. module: account
#: model:ir.model,name:account.model_account_fiscal_position_template
msgid "Template for Fiscal Position"
msgstr "Prijedlog za fiskalnu poziciju"

#. module: account
#: model:ir.model,name:account.model_account_account_template
msgid "Templates for Accounts"
msgstr "Prijedlozi konta"

#. module: account
#: model:ir.model,name:account.model_account_tax_template
msgid "Templates for Taxes"
msgstr "Prijedlozi poreza"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_line_form
msgid "Term Type"
msgstr "Tip termina"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term__line_ids
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_form
msgid "Terms"
msgstr "Termini"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_form
msgid "Terms and conditions..."
msgstr "Termini i uslovi..."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:57
#, python-format
msgid "That's on average"
msgstr "To je u prosjeku"

#. module: account
#: model:ir.model.fields,help:account.field_account_account__internal_group
#: model:ir.model.fields,help:account.field_account_account_type__internal_group
msgid ""
"The 'Internal Group' is used to filter accounts based on the internal group "
"set on the account type."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_account__internal_type
#: model:ir.model.fields,help:account.field_account_account_type__type
msgid ""
"The 'Internal Type' is used for features available on different types of "
"accounts: liquidity type is for cash or bank accounts, payable/receivable is"
" for vendor/customer accounts."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:1059
#, python-format
msgid "The account %s (%s) is deprecated."
msgstr ""

#. module: account
#: code:addons/account/models/account_invoice.py:521
#, python-format
msgid ""
"The account selected for payment does not belong to the same company as this"
" invoice."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_setup_bank_manual_config__journal_id
#: model:ir.model.fields,help:account.field_res_partner_bank__journal_id
msgid "The accounting journal corresponding to this bank account."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_config_settings__currency_exchange_journal_id
msgid ""
"The accounting journal where automatic exchange differences will be "
"registered"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line__amount_currency
#: model:ir.model.fields,help:account.field_account_move_line__amount_currency
msgid ""
"The amount expressed in an optional other currency if it is a multi-currency"
" entry."
msgstr "Iznos izražen u opcionalnoj drugoj valuti ako je viševalutni unos."

#. module: account
#: code:addons/account/models/account_move.py:696
#, python-format
msgid ""
"The amount expressed in the secondary currency must be positive when account"
" is debited and negative when account is credited."
msgstr ""

#. module: account
#: code:addons/account/models/account_bank_statement.py:319
#, python-format
msgid "The amount of a cash transaction cannot be 0."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:932
#, python-format
msgid ""
"The application scope of taxes in a group must be either the same as the "
"group or left empty."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:551
#, python-format
msgid ""
"The bank account of a bank journal must belong to the same company (%s)."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_company__account_bank_reconciliation_start
msgid ""
"The bank reconciliation widget won't ask to reconcile payments older than this date.\n"
"                                                                                                       This is useful if you install accounting after having used invoicing for some time and\n"
"                                                                                                       don't want to reconcile all the past payments with bank statements."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_config_settings__account_bank_reconciliation_start
msgid ""
"The bank reconciliation widget won't ask to reconcile payments older than this date.\n"
"               This is useful if you install accounting after having used invoicing for some time and\n"
"               don't want to reconcile all the past payments with bank statements."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__statement_id
msgid "The bank statement used for bank reconciliation"
msgstr "Bankovni izvod korišten za bankovno usklađivanje"

#. module: account
#: code:addons/account/models/account_invoice.py:1260
#, python-format
msgid ""
"The cash rounding cannot be computed because the difference must be added on the biggest tax found and no tax are specified.\n"
"Please set up a tax or change the cash rounding method."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_company__chart_template_id
msgid "The chart template for the company (if any)"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_bnk_stmt_check
msgid "The closing balance is different than the computed one!"
msgstr ""

#. module: account
#: sql_constraint:account.journal:0
msgid "The code and name of the journal must be unique per company !"
msgstr ""

#. module: account
#: sql_constraint:account.account:0
msgid "The code of the account must be unique per company !"
msgstr "Šifra konta mora biti jedinstvena za jednu kompaniju!"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__commercial_partner_id
msgid ""
"The commercial entity that will be used on Journal Entries for this invoice"
msgstr ""
"Komercijalni entitet koji će se koristiti na dnevničkim zapisima za ovu "
"fakturu"

#. module: account
#: code:addons/account/models/account.py:542
#, python-format
msgid ""
"The currency of the journal should be the same than the default credit "
"account."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:544
#, python-format
msgid ""
"The currency of the journal should be the same than the default debit "
"account."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__currency_id
msgid "The currency used to enter statement"
msgstr "Valuta koja se koristi pri unosu stavke"

#. module: account
#: code:addons/account/models/account_invoice.py:1981
#, python-format
msgid "The day of the month used for this term must be stricly positive."
msgstr ""

#. module: account
#: code:addons/account/models/account_bank_statement.py:193
#, python-format
msgid ""
"The ending balance is incorrect !\n"
"The expected balance (%s) is different from the computed one. (%s)"
msgstr ""

#. module: account
#: code:addons/account/models/account_fiscal_year.py:42
#, python-format
msgid "The ending date must not be prior to the starting date."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_product_category__property_account_expense_categ_id
msgid ""
"The expense is accounted for when a vendor bill is validated, except in "
"anglo-saxon accounting with perpetual inventory valuation in which case the "
"expense (Cost of Goods Sold account) is recognized at the customer invoice "
"validation."
msgstr ""

#. module: account
#: code:addons/account/models/account_invoice.py:891
#, python-format
msgid ""
"The field Vendor is required, please complete it to validate the Vendor "
"Bill."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_partner__property_account_position_id
msgid ""
"The fiscal position determines the taxes/accounts used for this contact."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:555
#, python-format
msgid "The holder of a journal's bank account must be the company (%s)."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line__account_id
msgid "The income or expense account related to the selected product."
msgstr "Račun prihoda ili troškova vezan za odabrani proizvod."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__code
msgid "The journal entries of this journal will be named using this prefix."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_company__account_opening_move_id
msgid ""
"The journal entry containing the initial balance of all this company's "
"accounts."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_financial_year_op__fiscalyear_last_day
#: model:ir.model.fields,help:account.field_account_financial_year_op__fiscalyear_last_month
msgid ""
"The last day of the month will be taken if the chosen day doesn't exist."
msgstr ""
"Posljednji datum u mjesecu će biti uzet ukoliko odabrani datum ne postoji."

#. module: account
#: code:addons/account/models/account_invoice.py:1892
#, python-format
msgid "The last line of a Payment Term should have the Balance type."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_form
msgid ""
"The last line's computation type should be \"Balance\" to ensure that the "
"whole amount will be allocated."
msgstr ""

#. module: account
#: code:addons/account/models/company.py:145
#, python-format
msgid "The lock date for advisors is irreversible and can't be removed."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__move_id
msgid "The move of this entry line."
msgstr "Prijenos ovog stavke unosa."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__name
msgid "The name that will be used on account move lines"
msgstr ""

#. module: account
#: code:addons/account/models/company.py:149
#, python-format
msgid ""
"The new lock date for advisors must be set after the previous lock date."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__refund_sequence_number_next
msgid "The next sequence number will be used for the next credit note."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__sequence_number_next
msgid "The next sequence number will be used for the next invoice."
msgstr ""

#. module: account
#: code:addons/account/models/account_invoice.py:1983
#, python-format
msgid "The number of days used for a payment term cannot be negative."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line__currency_id
#: model:ir.model.fields,help:account.field_account_move_line__currency_id
msgid "The optional other currency if it is a multi-currency entry."
msgstr "Opcionalna druga valuta ako je u pitanju viševalutni unos."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__quantity
msgid ""
"The optional quantity expressed by this line, eg: number of product sold. "
"The quantity is not a legal requirement but is very useful for some reports."
msgstr ""
"Opcionalna količina izražena ovom linijom, npr.: broj prodanih komada "
"artikla. Količina je vrlo korisna za neke izvještaje."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__account_id
msgid "The partner account used for this invoice."
msgstr "Račun partnera korišten za ovu fakturu"

#. module: account
#: model:ir.model.fields,help:account.field_res_partner__has_unreconciled_entries
msgid ""
"The partner has at least one unreconciled debit and credit since last time "
"the invoices & payments matching was performed."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:648
#, python-format
msgid ""
"The partners of the journal's company and the related bank account mismatch."
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:118
#, python-format
msgid "The payment amount cannot be negative."
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:602
#, python-format
msgid "The payment cannot be processed because the invoice is not open!"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__reference
msgid ""
"The payment communication that will be automatically populated once the "
"invoice validation. You can also write a free communication."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"The payments which have not been matched with a bank statement will not be "
"shown in bank reconciliation data if they were made before this date."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__match_partner_category_ids
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__match_partner_category_ids
msgid ""
"The reconciliation model will only be applied to the selected "
"customer/vendor categories."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__match_partner_ids
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__match_partner_ids
msgid ""
"The reconciliation model will only be applied to the selected "
"customers/vendors."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__match_nature
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__match_nature
msgid ""
"The reconciliation model will only be applied to the selected transaction type:\n"
"        * Amount Received: Only applied when receiving an amount.\n"
"        * Amount Paid: Only applied when paying an amount.\n"
"        * Amount Paid/Received: Applied in both cases."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__match_partner
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__match_partner
msgid ""
"The reconciliation model will only be applied when a customer/vendor is set."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__match_amount
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__match_amount
msgid ""
"The reconciliation model will only be applied when the amount being lower "
"than, greater than or between specified amount(s)."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__match_label
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__match_label
msgid ""
"The reconciliation model will only be applied when the label:\n"
"        * Contains: The proposition label must contains this string (case insensitive).\n"
"        * Not Contains: Negation of \"Contains\".\n"
"        * Match Regex: Define your own regular expression."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__match_journal_ids
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__match_journal_ids
msgid ""
"The reconciliation model will only be available from the selected journals."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__amount_residual_currency
msgid ""
"The residual amount on a journal item expressed in its currency (possibly "
"not the company currency)."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__amount_residual
msgid ""
"The residual amount on a journal item expressed in the company currency."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:681
#, python-format
msgid ""
"The selected account of your Journal Entry forces to provide a secondary "
"currency. You should remove the secondary currency on the account."
msgstr ""

#. module: account
#: code:addons/account/models/account_invoice.py:1763
#, python-format
msgid ""
"The selected unit of measure has to be in the same category as the product "
"unit of measure."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__sequence
#: model:ir.model.fields,help:account.field_account_tax_template__sequence
msgid ""
"The sequence field is used to define order in which the tax lines are "
"applied."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__match_total_amount_param
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__match_total_amount_param
msgid ""
"The sum of total residual amount propositions matches the statement line "
"amount under this percentage."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__match_total_amount
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__match_total_amount
msgid ""
"The sum of total residual amount propositions matches the statement line "
"amount."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_cash_rounding__rounding_method
msgid "The tie-breaking rule used for float rounding operations"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_my_invoices
msgid "There are currently no invoices and payments for your account."
msgstr ""

#. module: account
#: code:addons/account/models/company.py:248
#, python-format
msgid ""
"There are still unposted entries in the period you want to lock. You should "
"either post or delete them."
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:452
#, python-format
msgid ""
"There is no Transfer Account defined in the accounting settings. Please "
"define one to be able to confirm this transfer."
msgstr ""

#. module: account
#: code:addons/account/models/account_bank_statement.py:181
#, python-format
msgid ""
"There is no account defined on the journal %s for %s involved in a cash "
"difference."
msgstr ""

#. module: account
#: code:addons/account/wizard/account_validate_account_move.py:18
#, python-format
msgid "There is no journal items in draft state to post."
msgstr "Nema dnevničkih zapisa u statusu u pripremi za knjiženje."

#. module: account
#: code:addons/account/models/account_move.py:1566
#, python-format
msgid ""
"There is no tax cash basis journal defined for this company: \"%s\" \n"
"Configure it in Accounting/Configuration/Settings"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:48
#, python-format
msgid "There is nothing to reconcile."
msgstr "Nema ništa za zatvaranje stavki."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_invoice_error
msgid "There was an error processing this page."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "These taxes are set in any new product created."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_account_template__user_type_id
msgid ""
"These types are defined according to your country. The type contains more "
"information about the account and its specificities."
msgstr ""
"Ovi su tipovi definirani prema vašoj zemlji. Tip sadržava više informacija o"
" kontu i njegovim specifičnostima."

#. module: account
#: code:addons/account/models/account_invoice.py:1439
#, python-format
msgid ""
"This %s has been created from: <a href=# data-oe-model=account.invoice data-"
"oe-id=%d>%s</a><br>Reason: %s"
msgstr ""

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:100
#, python-format
msgid "This Week"
msgstr "Ove sedmice"

#. module: account
#: model:ir.model.fields,help:account.field_res_partner__property_account_payable_id
msgid ""
"This account will be used instead of the default one as the payable account "
"for the current partner"
msgstr ""
"Ovaj konto se koristi umjesto podrazumijevanog kao konto dugovanja prema "
"trenutnom partneru."

#. module: account
#: model:ir.model.fields,help:account.field_res_partner__property_account_receivable_id
msgid ""
"This account will be used instead of the default one as the receivable "
"account for the current partner"
msgstr ""
"Ovaj konte će se koristiti umjesto podrazumijevanog kao konto potraživanja "
"za trenutnog partnera"

#. module: account
#: model:ir.model.fields,help:account.field_product_category__property_account_income_categ_id
msgid "This account will be used when validating a customer invoice."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"This allows accountants to manage analytic and crossovered budgets. Once the"
" master budgets and the budgets are defined, the project managers can set "
"the planned amount on each analytic account."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_config_settings__module_account_batch_payment
msgid ""
"This allows you grouping payments into a single batch and eases the reconciliation process.\n"
"-This installs the account_batch_payment module."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"This allows you to manage the assets owned by a company or a person. It "
"keeps track of the depreciation occurred on those assets, and creates "
"account move for those depreciation lines."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"This allows you to manage the revenue recognition on selling products. It "
"keeps track of the installments occurred on those revenue recognitions, and "
"creates account moves for those installment lines."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_chart_template__complete_tax_set
msgid ""
"This boolean helps you to choose if you want to propose to the user to "
"encode the sale and purchase rates or choose from list of taxes. This last "
"choice assumes that the set of tax defined on this template is complete"
msgstr ""
"Ovaj izbor Vam pomaže odlučiti da li želite korisniku predložiti da unosi "
"stope poreza kod nabave i prodaje ili da odabere iz popisa poreza. Ovo drugo"
" podrazumijeva da je set poreza na ovom predlošku potpun."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "This feature is useful if you issue a high amounts of invoices."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__refund_sequence_id
msgid ""
"This field contains the information related to the numbering of the credit "
"note entries of this journal."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__sequence_id
msgid ""
"This field contains the information related to the numbering of the journal "
"entries of this journal."
msgstr ""
"Ovo polje sadržava informacije vezane uz sljedivosti dnevničkih zapisa"

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__journal_id
#: model:ir.model.fields,help:account.field_account_reconcile_model__second_journal_id
msgid "This field is ignored in a bank statement reconciliation."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__date_maturity
msgid ""
"This field is used for payable and receivable journal entries. You can put "
"the limit date for the payment of this line."
msgstr ""
"Koristi se za salda konti kupaca i dobavljača. Upišite datum valute "
"plaćanja."

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line__partner_name
msgid ""
"This field is used to record the third party name when importing bank "
"statement in electronic format, when the partner doesn't exist yet in the "
"database (or cannot be found)."
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.open_account_journal_dashboard_kanban
msgid "This is the accounting dashboard"
msgstr ""

#. module: account
#: code:addons/account/models/account.py:620
#, python-format
msgid ""
"This journal already contains items, therefore you cannot modify its "
"company."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:629
#, python-format
msgid ""
"This journal already contains items, therefore you cannot modify its short "
"name."
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:650
#, python-format
msgid ""
"This method should only be called to process a single invoice's payment."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_account_template__chart_template_id
msgid ""
"This optional field allow you to link an account template to a specific "
"chart template that may differ from the one its root parent belongs to. This"
" allow you to define chart templates that extend another and complete it "
"with few new accounts (You don't need to define the whole structure that is "
"common to both several times)."
msgstr ""
"Ova opcionalno polje Vam omogućava da povežete predložak konta na određeni "
"predložak kontnog plana koji se mogu razlikovati od onog kojem njegov "
"nadređeni pripada. To Vam omogućava da definišete predloške koji  proširuju "
"druge i upotpunjuju ih sa par novih konta (ne morate definisati cijelu "
"strukturu koja je zajednička za oba nekoliko puta)."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:38
#, python-format
msgid ""
"This page displays all the bank transactions that are to be reconciled and "
"provides with a neat interface to do so."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid ""
"This parameter will be bypassed in case of a statement line communication "
"matching exactly existing entries"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:294
#, python-format
msgid "This payment is registered but not reconciled."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_partner__property_supplier_payment_term_id
msgid ""
"This payment term will be used instead of the default one for purchase "
"orders and vendor bills"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_partner__property_payment_term_id
msgid ""
"This payment term will be used instead of the default one for sales orders "
"and customer invoices"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line__account_id
msgid ""
"This technical field can be used at the statement line creation/import time "
"in order to avoid the reconciliation process on it later on. The statement "
"line will simply create a counterpart on this account"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_validate_account_move
msgid ""
"This wizard will validate all journal entries selected. Once journal entries"
" are validated, you can not update them anymore."
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_reconcile_model
msgid ""
"Those can be used to quickly create a journal items when reconciling\n"
"                a bank statement or an account."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "To Invoice"
msgstr "Za fakturisati"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_tree
msgid "To Pay"
msgstr "Za platiti"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_tree
msgid "To pay"
msgstr "Za platiti"

#. module: account
#: code:addons/account/models/account_move.py:909
#, python-format
msgid "To reconcile the entries company should be the same for all entries."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:169
#, python-format
msgid "To speed up reconciliation, define"
msgstr ""

#. module: account
#: selection:account.invoice,activity_state:0
msgid "Today"
msgstr "Danas"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Today Activities"
msgstr "Današnje aktivnosti"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__amount_total
#: model:ir.model.fields,field_description:account.field_account_invoice_report__amount_total
#: model_terms:ir.ui.view,arch_db:account.invoice_form
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_tree
#: model_terms:ir.ui.view,arch_db:account.invoice_tree
#: model_terms:ir.ui.view,arch_db:account.view_account_bnk_stmt_cashbox
msgid "Total"
msgstr "Ukupno"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_tree
msgid "Total Amount"
msgstr "Ukupan iznos"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_line_tax_audit_tree
msgid "Total Base Amount"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
#: model_terms:ir.ui.view,arch_db:account.view_move_line_tree
msgid "Total Credit"
msgstr "Ukupno potražuje"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
#: model_terms:ir.ui.view,arch_db:account.view_move_line_tree
msgid "Total Debit"
msgstr "Ukupan dug"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__total_invoiced
msgid "Total Invoiced"
msgstr "Ukupno fakturisano"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__debit
msgid "Total Payable"
msgstr "Ukupno potražuje"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__credit
msgid "Total Receivable"
msgstr "Ukupno duguje"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__user_currency_residual
msgid "Total Residual"
msgstr "Ukupno preostalo"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__user_currency_price_total
msgid "Total Without Tax in Currency"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line__price_subtotal_signed
msgid "Total amount in the currency of the company, negative for credit note."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__amount_total_company_signed
msgid ""
"Total amount in the currency of the company, negative for credit notes."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__amount_total_signed
msgid ""
"Total amount in the currency of the invoice, negative for credit notes."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_partner__credit
msgid "Total amount this customer owes you."
msgstr "Ukupan iznos koji duguje ovaj kupca."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line__price_total
msgid "Total amount with taxes"
msgstr "Ukupan iznos sa porezom"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line__price_subtotal
msgid "Total amount without taxes"
msgstr "Ukupan iznos bez poreza"

#. module: account
#: model:ir.model.fields,help:account.field_res_partner__debit
msgid "Total amount you have to pay to this vendor."
msgstr "Ukupni iznos za plaćanje dobavljaču."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__amount_total_company_signed
msgid "Total in Company Currency"
msgstr "Ukupno u valuti kompanije"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__amount_total_signed
msgid "Total in Invoice Currency"
msgstr "Ukupno u valuti fakture"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__total_entry_encoding
msgid "Total of transaction lines."
msgstr "Ukupno transakcijske stavke"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Track costs &amp; revenues by project, department, etc."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__tracking_value_ids
msgid ""
"Tracked values are stored in a separate model. This field allow to "
"reconstruct the tracking and to generate statistics on the model."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__tracking_value_ids
msgid "Tracking values"
msgstr "Vrijednosti praćenja"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:304
#, python-format
msgid "Transaction"
msgstr "Transakcija"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_form
msgid "Transactions"
msgstr "Transakcije"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__total_entry_encoding
msgid "Transactions Subtotal"
msgstr "Ukupno transakcije"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__destination_journal_id
msgid "Transfer To"
msgstr "Prenesi novčana sredstva na"

#. module: account
#: code:addons/account/models/account_payment.py:714
#, python-format
msgid "Transfer from %s"
msgstr "Prenesi novčana sredstva od %s"

#. module: account
#: code:addons/account/models/account_payment.py:790
#, python-format
msgid "Transfer to %s"
msgstr "Prenesi novčana sredstva na %s"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Transfers"
msgstr "Za prenos"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__user_type_id
#: model:ir.model.fields,field_description:account.field_account_account_template__user_type_id
#: model:ir.model.fields,field_description:account.field_account_account_type__type
#: model:ir.model.fields,field_description:account.field_account_bank_statement__journal_type
#: model:ir.model.fields,field_description:account.field_account_invoice__type
#: model:ir.model.fields,field_description:account.field_account_invoice_report__type
#: model:ir.model.fields,field_description:account.field_account_invoice_send__message_type
#: model:ir.model.fields,field_description:account.field_account_journal__type
#: model:ir.model.fields,field_description:account.field_account_move_line__user_type_id
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__value
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__rule_type
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__rule_type
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__acc_type
msgid "Type"
msgstr "Tip"

#. module: account
#: selection:account.cash.rounding,rounding_method:0
msgid "UP"
msgstr ""

#. module: account
#: code:addons/account/models/account.py:398
#, python-format
msgid "Undefined Yet"
msgstr "Još nedefinisan"

#. module: account
#: code:addons/account/models/company.py:363
#, python-format
msgid "Undistributed Profits/Losses"
msgstr "Neraspoređena Dobit/Gubitak"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line__price_unit
msgid "Unit Price"
msgstr "Jedinična cijena"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_form
msgid "Unit Price:"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line__uom_id
#: model:ir.model.fields,field_description:account.field_account_move_line__product_uom_id
msgid "Unit of Measure"
msgstr "Jedinica mjere"

#. module: account
#: code:addons/account/report/account_aged_partner_balance.py:207
#, python-format
msgid "Unknown Partner"
msgstr "Nepoznat partner"

#. module: account
#: model:ir.actions.act_window,name:account.act_account_journal_2_account_invoice_opened
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Unpaid Invoices"
msgstr "Neplaćene fakture"

#. module: account
#: selection:account.move,state:0
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Unposted"
msgstr "Neproknjiženo"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Unposted Journal Entries"
msgstr "Neproknjižene stavke dnevnika"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Unposted Journal Items"
msgstr "Neproknjižene stavke dnevnika"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_unread
#: model:ir.model.fields,field_description:account.field_account_invoice__message_unread
#: model:ir.model.fields,field_description:account.field_account_payment__message_unread
msgid "Unread Messages"
msgstr "Nepročitane poruke"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_unread_counter
#: model:ir.model.fields,field_description:account.field_account_invoice__message_unread_counter
#: model:ir.model.fields,field_description:account.field_account_payment__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Brojač nepročitanih poruka"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:81
#: model_terms:ir.ui.view,arch_db:account.account_unreconcile_view
#, python-format
msgid "Unreconcile"
msgstr "Otkaži zatvaranje"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_unreconcile
msgid "Unreconcile Entries"
msgstr "Otkaži zatvaranje zapisa"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_unreconcile_view
msgid "Unreconcile Transactions"
msgstr "Otkaži zatvaranje transakcija"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Unreconciled"
msgstr "Ne zatvoreni"

#. module: account
#: model:ir.actions.act_window,name:account.act_account_acount_move_line_open_unreconciled
msgid "Unreconciled Entries"
msgstr "Ne zatvoreni zapisi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__amount_untaxed
msgid "Untaxed Amount"
msgstr "Neoporezivi iznos"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__amount_untaxed_signed
msgid "Untaxed Amount in Company Currency"
msgstr "Neoporezovani iznos u valuti kompanije"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__price_total
msgid "Untaxed Total"
msgstr "Ukupno neoporezovano"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Update exchange rates automatically"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/bills_tree_upload_views.xml:5
#, python-format
msgid "Upload"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_import_wizard_form_view
msgid "Upload Files"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__use_anglo_saxon
msgid "Use Anglo-Saxon accounting"
msgstr "Koristi Anglo-Saxonsko računovodstvo"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__tax_exigibility
msgid "Use Cash Basis"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_sepa_direct_debit
msgid "Use SEPA Direct Debit"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_reversal__journal_id
msgid "Use Specific Journal"
msgstr "Koristi specifični dnevnik"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__use_active_domain
msgid "Use active domain"
msgstr "Koristi aktivni domen"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__anglo_saxon_accounting
msgid "Use anglo-saxon accounting"
msgstr "Koristi Anglo-Saxonsko računovodstvo"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_batch_payment
msgid "Use batch payments"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Use budgets to compare actual with expected revenues and costs"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Use depreciation boards, automate amortization entries"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Use follow-up levels and schedule actions"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__group_products_in_bills
msgid "Use products in vendor bills"
msgstr ""

#. module: account
#: model:res.groups,name:account.group_products_in_bills
msgid "Use products on vendor bills"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__template_id
msgid "Use template"
msgstr "Koristi predložak"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_refund
msgid ""
"Use this option if you want to cancel an invoice and create a new\n"
"                                one. The credit note will be created, validated and reconciled\n"
"                                with the current invoice. A new draft invoice will be created\n"
"                                so that you can edit it."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_refund
msgid ""
"Use this option if you want to cancel an invoice you should not\n"
"                                have issued. The credit note will be created, validated and reconciled\n"
"                                with the invoice. You will not be able to modify the credit note."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_account_type__include_initial_balance
msgid ""
"Used in reports to know if we should consider journal items from the "
"beginning of time instead of from the fiscal year only. Account types that "
"should be reset to zero at each new fiscal year (like expenses, revenue..) "
"should not have this option set."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__reference
msgid ""
"Used to hold the reference of the external mean that created this statement "
"(name of imported file, reference of online synchronization...)"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__sequence
msgid "Used to order Journals in the dashboard view"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__loss_account_id
msgid ""
"Used to register a loss when the ending balance of a cash register differs "
"from what the system computes"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__profit_account_id
msgid ""
"Used to register a profit when the ending balance of a cash register differs"
" from what the system computes"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line__journal_currency_id
#: model:ir.model.fields,help:account.field_account_move_line__company_currency_id
#: model:ir.model.fields,help:account.field_account_partial_reconcile__company_currency_id
#: model:ir.model.fields,help:account.field_res_partner__currency_id
msgid "Utility field to express amount currency"
msgstr "Pomoćno polje da istakne iznos u valuti"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__vat_required
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__vat_required
msgid "VAT required"
msgstr "PDV obavezan"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:78
#: model_terms:ir.ui.view,arch_db:account.invoice_form
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_form
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_from_invoices
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_invoice_form
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_form
#, python-format
msgid "Validate"
msgstr "Odobri"

#. module: account
#: model:ir.model,name:account.model_validate_account_move
msgid "Validate Account Move"
msgstr "Odobri knjiženje"

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__auto_reconcile
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__auto_reconcile
msgid ""
"Validate the statement line automatically (reconciliation based on your "
"rule)."
msgstr ""

#. module: account
#: selection:account.bank.statement,state:0
#: model:mail.message.subtype,name:account.mt_invoice_validated
msgid "Validated"
msgstr "Odobreno"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__value_amount
msgid "Value"
msgstr "Vrijednost"

#. module: account
#: selection:account.abstract.payment,partner_type:0
#: selection:account.payment,partner_type:0
#: selection:account.register.payments,partner_type:0
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_form
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_tree
#: model_terms:ir.ui.view,arch_db:account.view_account_supplier_payment_tree
msgid "Vendor"
msgstr "Dobavljač"

#. module: account
#: selection:account.invoice,type:0 selection:account.invoice.report,type:0
#: code:addons/account/models/account_invoice.py:488
#: code:addons/account/models/account_invoice.py:1315
#: model:ir.model.fields,field_description:account.field_account_invoice__vendor_bill_id
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_form
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_tree
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
#, python-format
msgid "Vendor Bill"
msgstr "Račun dobavljača"

#. module: account
#: code:addons/account/models/account_invoice.py:489
#, python-format
msgid "Vendor Bill - %s"
msgstr "Račun dobavljača - %s"

#. module: account
#: code:addons/account/models/chart_template.py:410
#: model:ir.actions.act_window,name:account.action_vendor_bill_template
#, python-format
msgid "Vendor Bills"
msgstr "Računi dobavljača"

#. module: account
#: selection:account.invoice,type:0 selection:account.invoice.report,type:0
#: code:addons/account/models/account_invoice.py:490
#: code:addons/account/models/account_payment.py:772
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
#, python-format
msgid "Vendor Credit Note"
msgstr "Knjižno odobrenje dobavljača"

#. module: account
#: code:addons/account/models/account_invoice.py:491
#, python-format
msgid "Vendor Credit Note - %s"
msgstr "Knjižno odobrenje dobavljača - %s"

#. module: account
#: code:addons/account/models/account_invoice.py:1317
#, python-format
msgid "Vendor Credit note"
msgstr "Knjižno odobrenje dobavljača"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice__vendor_display_name
msgid "Vendor Display Name"
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:774
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#, python-format
msgid "Vendor Payment"
msgstr "Plaćanja dobavljaču"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__property_supplier_payment_term_id
msgid "Vendor Payment Terms"
msgstr "Uslovi plaćanja dobavljača"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Vendor Payments"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Vendor Reference"
msgstr "Referenca dobavljača"

#. module: account
#: model:ir.model.fields,field_description:account.field_product_template__supplier_taxes_id
msgid "Vendor Taxes"
msgstr "Porezi dobavljača"

#. module: account
#: model:ir.ui.menu,name:account.menu_account_supplier
#: model:ir.ui.menu,name:account.menu_finance_payables
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Vendors"
msgstr "Dobavljači"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:82
#, python-format
msgid "View"
msgstr "Pregled"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_partner_property_form
msgid "View accounts detail"
msgstr ""

#. module: account
#: selection:res.partner,invoice_warn:0
msgid "Warning"
msgstr "Upozorenje"

#. module: account
#: code:addons/account/models/account_invoice.py:778
#, python-format
msgid "Warning for %s"
msgstr "Upozorenje za %s"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.partner_view_buttons
msgid "Warning on the Invoice"
msgstr "upozorenje na Fakturi"

#. module: account
#: code:addons/account/models/account_invoice.py:1682
#: code:addons/account/models/account_invoice.py:1762
#, python-format
msgid "Warning!"
msgstr "Upozorenje!"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Warnings"
msgstr "Upozorenja"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__group_warning_account
msgid "Warnings in Invoices"
msgstr ""

#. module: account
#: code:addons/account/models/company.py:518
#, python-format
msgid ""
"We cannot find a chart of accounts for this company, you should configure it. \n"
"Please go to Account Configuration and select or install a fiscal localization."
msgstr ""

#. module: account
#: code:addons/account/models/company.py:457
#, python-format
msgid ""
"We cannot find any journal for this company. You should create one.\n"
"Please go to Configuration > Journals."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__website_message_ids
#: model:ir.model.fields,field_description:account.field_account_invoice__website_message_ids
#: model:ir.model.fields,field_description:account.field_account_payment__website_message_ids
msgid "Website Messages"
msgstr "Poruke sa website-a"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__website_message_ids
#: model:ir.model.fields,help:account.field_account_invoice__website_message_ids
#: model:ir.model.fields,help:account.field_account_payment__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"When receiving an email with a bill, or uploading scanned bills, Odoo will "
"parse them (OCR) and auto-complete (AI) Draft Bills to validate."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__post_at_bank_rec
msgid ""
"Whether or not the payments made in this journal should be generated in "
"draft state, so that the related journal entries are only posted when "
"performing bank reconciliation."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__is_log
msgid "Whether the message is an internal note (comment mode only)"
msgstr "Dali je poruka interna zabilješka (samo mod komentarisanja)"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__show_on_dashboard
msgid "Whether this journal should be displayed on the dashboard or not"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_setup_bank_manual_config__new_journal_name
msgid "Will be used to name the Journal related to this bank account"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__amount_currency
#: model:ir.model.fields,field_description:account.field_account_print_journal__amount_currency
msgid "With Currency"
msgstr "S valutom"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_search
msgid "With tax"
msgstr "Sa porezom"

#. module: account
#: code:addons/account/models/account_move.py:979
#: code:addons/account/models/account_move.py:999
#, python-format
msgid "Write-Off"
msgstr "Otpis duga"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__amount
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__amount
msgid "Write-off Amount"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Counterpart Values"
msgstr ""

#. module: account
#: sql_constraint:account.move.line:0
msgid "Wrong credit or debit value in accounting entry !"
msgstr "Pogrešna dugovna ili potražna vrijednost upisane stavke!"

#. module: account
#: code:addons/account/models/account_move.py:907
#, python-format
msgid "You are trying to reconcile some entries that are already reconciled."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__blocked
msgid ""
"You can check this box to mark this journal item as a litigation with the "
"associated partner"
msgstr ""
"Možete označiti ovaj okvir kako bi obilježili  stavku dnevničkog zapisa kao "
"poveznicu s pripadajućim partnerom."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__partner_id
#: model:ir.model.fields,help:account.field_account_invoice_line__partner_id
msgid "You can find a contact by its Name, TIN, Email or Internal Reference."
msgstr ""

#. module: account
#: code:addons/account/models/account_invoice.py:1940
#, python-format
msgid ""
"You can not delete payment terms as other records still reference it. "
"However, you can archive it."
msgstr ""

#. module: account
#: code:addons/account/models/account_fiscal_year.py:55
#, python-format
msgid ""
"You can not have an overlap between two fiscal years, please correct the "
"start and/or end dates of your fiscal years."
msgstr ""

#. module: account
#: code:addons/account/models/account_invoice.py:1784
#, python-format
msgid "You can only delete an invoice line if the invoice is in draft state."
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:86
#, python-format
msgid "You can only register payments for open invoices"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_company__invoice_reference_type
msgid ""
"You can set here the default communication that will appear on customer "
"invoices, once validated, to help the customer to refer to that particular "
"invoice when making the payment."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:370
#, python-format
msgid ""
"You cannot add/modify entries prior to and inclusive of the lock date %s"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:372
#, python-format
msgid ""
"You cannot add/modify entries prior to and inclusive of the lock date %s. "
"Check the company settings or ask someone with the 'Adviser' role"
msgstr ""

#. module: account
#: code:addons/account/models/company.py:267
#, python-format
msgid ""
"You cannot change the currency of the company since some journal items "
"already exist"
msgstr ""

#. module: account
#: code:addons/account/models/account.py:310
#, python-format
msgid ""
"You cannot change the owner company of an account that already contains "
"journal items."
msgstr ""
"Ne možete mijenjati kompaniju vlasnika na kontu koji već sadrži dnevničke "
"zapise."

#. module: account
#: code:addons/account/models/account_move.py:688
#, python-format
msgid ""
"You cannot create journal items with a secondary currency without filling "
"both 'currency' and 'amount currency' fields."
msgstr ""

#. module: account
#: code:addons/account/models/company.py:171
#, python-format
msgid ""
"You cannot define stricter conditions on advisors than on users. Please make"
" sure that the lock date on advisor is set before the lock date for users."
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:583
#, python-format
msgid "You cannot delete a payment that is already posted."
msgstr ""

#. module: account
#: code:addons/account/models/account_invoice.py:729
#, python-format
msgid ""
"You cannot delete an invoice after it has been validated (and received a "
"number). You can set it back to \"Draft\" state and modify its content, then"
" re-confirm it."
msgstr ""

#. module: account
#: code:addons/account/models/account_invoice.py:727
#, python-format
msgid ""
"You cannot delete an invoice which is not draft or cancelled. You should "
"create a credit note instead."
msgstr ""

#. module: account
#: code:addons/account/models/res_config_settings.py:146
#, python-format
msgid ""
"You cannot disable this setting because some of your taxes are cash basis. "
"Modify your taxes first before disabling this setting."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:1146
#, python-format
msgid ""
"You cannot do this modification on a posted journal entry, you can just change some non legal fields. You must revert the journal entry to cancel it.\n"
"%s."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:1148
#, python-format
msgid ""
"You cannot do this modification on a reconciled entry. You can just change some non legal fields or you must unreconcile first.\n"
"%s."
msgstr ""
"Ne možete vršiti ovu modifikaciju na zatvorenoj stavci. Možete samo mijenjati neka nevažna polja ili morate razvezati stavke.\n"
"%s."

#. module: account
#: code:addons/account/models/account.py:64
#, python-format
msgid ""
"You cannot have a receivable/payable account that is not reconcilable. "
"(account code: %s)"
msgstr ""

#. module: account
#: code:addons/account/models/account.py:71
#, python-format
msgid ""
"You cannot have more than one account with \"Current Year Earnings\" as "
"type. (accounts: %s)"
msgstr ""

#. module: account
#: code:addons/account/models/company.py:160
#, python-format
msgid ""
"You cannot lock a period that is not finished yet. Please make sure that the"
" lock date for advisors is not set after the last day of the previous month."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_model.js:741
#, python-format
msgid "You cannot mix items from receivable and payable accounts."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:124
#, python-format
msgid "You cannot modify a journal entry linked to a posted payment."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:331
#, python-format
msgid ""
"You cannot modify a posted entry of this journal.\n"
"First you should set the journal to allow cancelling entries."
msgstr ""
"Ne možete mijenjati knjižene stavke ovog dnevnika.\n"
"Prvo je potrebno u dnevniku omogućiti otkazivanje stavki."

#. module: account
#: code:addons/account/models/account_invoice.py:909
#, python-format
msgid ""
"You cannot pay an invoice which is partially paid. You need to reconcile "
"payment entries first."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:327
#, python-format
msgid ""
"You cannot perform this action on an account that contains journal items."
msgstr ""

#. module: account
#: code:addons/account/wizard/pos_box.py:36
#, python-format
msgid "You cannot put/take money in/out for a bank statement which is closed."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:644
#, python-format
msgid "You cannot remove the bank account from the journal once set."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:332
#, python-format
msgid ""
"You cannot remove/deactivate an account which is set on a customer or "
"vendor."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:320
#, python-format
msgid ""
"You cannot set a currency on this account as it already has some journal "
"entries having a different foreign currency."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:294
#, python-format
msgid ""
"You cannot switch an account to prevent the reconciliation if some partial "
"reconciliations are still pending."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:1112
#, python-format
msgid "You cannot use a deprecated account."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:1069
#, python-format
msgid ""
"You cannot use this general account in this journal, check the tab 'Entry "
"Controls' on the related journal."
msgstr ""
"Ne možete koristiti ovaj opšti konto u ovom dnevniku. Provjerite tab "
"'kontola unosa' na povezanom dnevniku."

#. module: account
#: code:addons/account/models/account_invoice.py:76
#: code:addons/account/models/account_invoice.py:895
#, python-format
msgid ""
"You cannot validate an invoice with a negative total amount. You should "
"create a credit note instead."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_form
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "You have"
msgstr "Imate"

#. module: account
#: code:addons/account/models/account_payment.py:622
#, python-format
msgid "You have to define a sequence for %s in your company."
msgstr ""

#. module: account
#: code:addons/account/wizard/pos_box.py:50
#: code:addons/account/wizard/pos_box.py:69
#, python-format
msgid ""
"You have to define an 'Internal Transfer Account' in your cash register's "
"journal."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:142
#, python-format
msgid "You must first define an opening move."
msgstr ""

#. module: account
#: code:addons/account/models/account_invoice.py:1683
#, python-format
msgid "You must first select a partner."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:55
#, python-format
msgid "You reconciled"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:1624
#, python-format
msgid ""
"You should configure the 'Exchange Rate Journal' in the accounting settings,"
" to manage automatically the booking of accounting entries related to "
"differences between exchange rates."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:1626
#, python-format
msgid ""
"You should configure the 'Gain Exchange Rate Account' in the accounting "
"settings, to manage automatically the booking of accounting entries related "
"to differences between exchange rates."
msgstr ""
"Trebali bi ste podesiti 'Konto pozitivne kursne razlike' u postavkama "
"računovodstva, za automatsko knjiženje kursnih razlika."

#. module: account
#: code:addons/account/models/account_move.py:1628
#, python-format
msgid ""
"You should configure the 'Loss Exchange Rate Account' in the accounting "
"settings, to manage automatically the booking of accounting entries related "
"to differences between exchange rates."
msgstr ""
"Kako bi automatski knjižili stavke dnevnika vezane za ralike kursa, trebate "
"konfigurisati 'konto negativnih kursnih razlika' u postavkama računovodstva."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_refund
msgid ""
"You will be able to edit and validate this\n"
"                                credit note directly or keep it draft,\n"
"                                waiting for the document to be issued by\n"
"                                your supplier/customer."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_position_form
msgid "Zip Range"
msgstr "Raspon poštanskih brojeva"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__zip_from
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__zip_from
msgid "Zip Range From"
msgstr "Raspon poštanskih brojeva od"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__zip_to
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__zip_to
msgid "Zip Range To"
msgstr "Raspon poštanskih brojeva do"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_dashboard_onboarding_panel
msgid "action_close_account_dashboard_onboarding"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_onboarding_panel
msgid "action_close_account_invoice_onboarding"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:17
#, python-format
msgid "assign to invoice"
msgstr "dodjeli fakturi"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_invoice_error
#: model_terms:ir.ui.view,arch_db:account.portal_invoice_success
msgid "close"
msgstr "zatvori"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_form
msgid "code"
msgstr "šifra"

#. module: account
#: selection:account.payment.term.line,option:0
msgid "day(s) after the end of the invoice month"
msgstr ""

#. module: account
#: selection:account.payment.term.line,option:0
msgid "day(s) after the invoice date"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.setup_bank_account_wizard
msgid "e.g ****************"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.setup_bank_account_wizard
msgid "e.g Bank of America"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.setup_bank_account_wizard
msgid "e.g Checking account"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "e.g. Bank Fees"
msgstr "npr.: Provizije banke"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_form
msgid "for this customer. You can allocate them to mark this invoice as paid."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "for this supplier. You can allocate them to mark this bill as paid."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:318
#, python-format
msgid "have been reconciled automatically."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:44
#, python-format
msgid "o_manual_statement"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_onboarding_panel
msgid "o_onboarding_blue"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_dashboard_onboarding_panel
msgid "o_onboarding_orange"
msgstr ""

#. module: account
#: selection:account.payment.term.line,option:0
msgid "of the current month"
msgstr ""

#. module: account
#: selection:account.payment.term.line,option:0
msgid "of the following month"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "outstanding debits"
msgstr "zakašnjeli dugovi"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.invoice_form
msgid "outstanding payments"
msgstr "zakašnjela plaćanja"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:169
#, python-format
msgid "reconciliation models"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:154
#, python-format
msgid "remaining)"
msgstr "preostalo)"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_dashboard_onboarding_panel
#: model_terms:ir.ui.view,arch_db:account.account_invoice_onboarding_panel
msgid "res.company"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:57
#, python-format
msgid "seconds per transaction."
msgstr "sekundi po transakciji."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:319
#, python-format
msgid "statement lines"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_partner_property_form
msgid "the parent company"
msgstr "nadređena kompanija"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:55
#, python-format
msgid "transactions in"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice__amount_by_group
msgid "type: [(name, amount, base, formated amount, formated base)]"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_form
msgid "→ Count"
msgstr "→ Count"
