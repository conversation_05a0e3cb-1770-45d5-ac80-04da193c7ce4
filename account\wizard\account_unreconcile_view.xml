<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="account_unreconcile_view" model="ir.ui.view">
            <field name="name">Unreconcile Entries</field>
            <field name="model">account.unreconcile</field>
            <field name="arch" type="xml">
            <form string="Unreconcile">
                <separator string="Unreconcile Transactions"/>
                <form class="o_form_label">If you unreconcile transactions, you must also verify all the actions that are linked to those transactions because they will not be disabled</form>
                <footer>
                    <button string="Unreconcile" name="trans_unrec" type="object" default_focus="1" class="btn-primary" data-hotkey="q"/>
                    <button string="Cancel" class="btn-secondary" special="cancel" data-hotkey="z"/>
                </footer>
            </form>
            </field>
        </record>

        <record id="action_account_unreconcile" model="ir.actions.act_window">
            <field name="name">Unreconcile</field>
            <field name="groups_id" eval="[(4, ref('account.group_account_user'))]"/>
            <field name="res_model">account.unreconcile</field>
            <field name="view_mode">form</field>
            <field name="view_id" ref="account_unreconcile_view"/>
            <field name="target">new</field>
            <field name="binding_model_id" ref="account.model_account_move_line" />
            <field name="binding_view_types">list</field>
        </record>

    </data>
</odoo>
