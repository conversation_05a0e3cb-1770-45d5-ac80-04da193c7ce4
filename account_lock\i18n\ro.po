# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_lock
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <fold<PERSON><EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:19+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"Any new All Users Lock Date must be posterior (or equal) to the previous "
"one."
msgstr ""
"Orice nouă dată de blocare a tuturor utilizatorilor trebuie să fie "
"posterioară (sau egală) cu cea precedentă."

#. module: account_lock
#: model:ir.model,name:account_lock.model_res_company
msgid "Companies"
msgstr "Companii"

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid "The lock date for advisors is irreversible and can't be removed."
msgstr ""
"Data de blocare a auditorilor este ireversibilă și nu poate fi eliminată."

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid "The new tax lock date must be set after the previous lock date."
msgstr ""
"Noua dată de blocare a impozitului trebuie stabilită după data anterioară de"
" blocare"

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid "The tax lock date is irreversible and can't be removed."
msgstr ""
"Data de blocare a impozitului este ireversibilă și nu poate fi eliminată"

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"You cannot lock a period that has not yet ended. Therefore, the All Users "
"Lock Date must be anterior (or equal) to the last day of the previous month."
msgstr ""
"Nu puteți bloca o perioadă care nu s-a încheiat încă. Deci, data de blocare "
"a tuturor utilizatorilor trebuie să fie anterioară (sau egală) cu ultima zi "
"a lunii anterioare."

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"You cannot lock a period that has not yet ended. Therefore, the tax lock "
"date must be anterior (or equal) to the last day of the previous month."
msgstr ""
"Nu puteți bloca o perioadă care nu s-a încheiat încă. Deci, data de blocare "
"a impozitului trebuie să fie anterioară (sau egală) cu ultima zi a lunii "
"anterioare."

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"You cannot set stricter restrictions on advisors than on users. Therefore, "
"the All Users Lock Date must be anterior (or equal) to the Invoice/Bills "
"Lock Date."
msgstr ""
"Nu puteți stabili restricții mai stricte pentru auditori decât pentru "
"utilizatori. Deci, data de blocare a tuturor utilizatorilor trebuie să fie "
"anterioară (sau egală) cu data de blocare a facturilor / facturilor."
