<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- ONBOARDING STEPS -->
    <template id="onboarding_invoice_layout_step">
        <t t-call="base.onboarding_step">
            <t t-set="title">Invoice Layout</t>
            <t t-set="description">Customize the look of your invoices.</t>
            <t t-set="done_icon" t-value="'fa-star'" />
            <t t-set="btn_text">Customize</t>
            <t t-set="done_text">Looks great!</t>
            <t t-set="method" t-value="'action_open_base_document_layout'" />
            <t t-set="model" t-value="'base.document.layout'" />
            <t t-set="state" t-value="state.get('account_onboarding_invoice_layout_state')" />
        </t>
    </template>
    <template id="onboarding_create_invoice_step">
        <t t-call="base.onboarding_step">
            <t t-set="title">Create Invoice</t>
            <t t-set="description">
                Create your first invoice.
            </t>
            <t t-set="btn_text">Create</t>
            <t t-set="done_text">First invoice sent!</t>
            <t t-set="method" t-value="'action_open_account_onboarding_create_invoice'" />
            <t t-set="model" t-value="'res.company'" />
            <t t-set="state" t-value="state.get('account_onboarding_create_invoice_state')" />
        </t>
    </template>
    <template id="onboarding_bank_account_step">
        <t t-call="base.onboarding_step">
            <t t-set="title">Bank Account</t>
            <t t-set="description">
                Connect your financial accounts in seconds.
            </t>
            <t t-set="btn_text">Add a bank account</t>
            <t t-set="done_text">Step Completed!</t>
            <t t-set="method" t-value="'setting_init_bank_account_action'" />
            <t t-set="model" t-value="'res.company'" />
            <t t-set="state" t-value="state.get('account_setup_bank_data_state')" />
        </t>
    </template>
    <template id="onboarding_fiscal_year_step">
        <t t-call="base.onboarding_step">
            <t t-set="title">Accounting Periods</t>
            <t t-set="description">Define your fiscal years &amp; tax returns periodicity.</t>
            <t t-set="btn_text">Configure</t>
            <t t-set="done_text">Step Completed!</t>
            <t t-set="method" t-value="'setting_init_fiscal_year_action'" />
            <t t-set="model" t-value="'res.company'" />
            <t t-set="state" t-value="state.get('account_setup_fy_data_state')" />
        </t>
    </template>
    <template id="onboarding_chart_of_account_step">
        <t t-call="base.onboarding_step">
            <t t-set="title">Chart of Accounts</t>
            <t t-set="description">Setup your chart of accounts and record initial balances.</t>
            <t t-set="btn_text">Review</t>
            <t t-set="done_text">Chart of accounts set.</t>
            <t t-set="method" t-value="'setting_chart_of_accounts_action'" />
            <t t-set="model" t-value="'res.company'" />
            <t t-set="state" t-value="state.get('account_setup_coa_state')" />
        </t>
    </template>
    <template id="onboarding_taxes_step">
        <t t-call="base.onboarding_step">
            <t t-set="title">Taxes</t>
            <t t-set="description">Set default Taxes for sales and purchase transactions.</t>
            <t t-set="btn_text">Review</t>
            <t t-set="done_text">Taxes set.</t>
            <t t-set="method" t-value="'action_open_taxes_onboarding'" />
            <t t-set="model" t-value="'res.company'" />
            <t t-set="state" t-value="state.get('account_setup_taxes_state')" />
        </t>
    </template>
    <!-- ONBOARDING PANELS -->
    <template id="account_invoice_onboarding_panel" name="account.invoice.onboarding.panel">
        <t t-call="base.onboarding_container">
            <t t-set="classes" t-value="'o_onboarding_blue'"/>
            <t t-set="bg_image" t-value="'/account/static/src/img/account_invoice_onboarding_bg.jpg'"/>
            <t t-set="close_method" t-value="'action_close_account_invoice_onboarding'"/>
            <t t-set="close_model" t-value="'res.company'"/>
            <t t-set="text_completed">Congratulations! You are all set.</t>

            <t t-call="base.onboarding_company_step" name="company_step" />
            <t t-call="account.onboarding_bank_account_step" name="bank_account_step" t-if="not env.user.has_group('account.group_account_user')"/>
            <t t-call="account.onboarding_invoice_layout_step" name="invoice_layout_step" />
            <t t-call="account.onboarding_create_invoice_step" name="create_invoice_step" />
        </t>
    </template>
    <template id="account_dashboard_onboarding_panel" name="account.dashboard.onboarding.panel">
        <t t-call="base.onboarding_container">
            <t t-set="classes" t-value="'o_onboarding_orange'"/>
            <t t-set="bg_image" t-value="'/account/static/src/img/account_dashboard_onboarding_bg.jpg'"/>
            <t t-set="close_method" t-value="'action_close_account_dashboard_onboarding'"/>
            <t t-set="close_model" t-value="'res.company'"/>

            <t t-call="account.onboarding_fiscal_year_step" name="fiscal_year_step" />
            <t t-call="account.onboarding_chart_of_account_step" name="chart_of_account_step" />
            <t t-call="account.onboarding_taxes_step" name="taxes_step" />
            <t t-call="account.onboarding_bank_account_step" name="bank_account_step" />
        </t>
    </template>
    <!-- SAMPLE INVOICE EMAIL -->
    <record id="action_open_account_onboarding_create_invoice" model="ir.actions.act_window">
        <field name="name">Create first invoice</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">account.move</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="account.view_move_form" />
        <field name="context">{'default_move_type': 'out_invoice'}</field>
    </record>
    <!-- SALE TAX -->
    <template id="onboarding_sale_tax_step">
        <t t-call="base.onboarding_step">
            <t t-set="title">Taxes</t>
            <t t-set="description">Choose a default sales tax for your products.</t>
            <t t-set="btn_text">Set taxes</t>
            <t t-set="done_text">Step Completed!</t>
            <t t-set="method" t-value="'action_open_account_onboarding_sale_tax'" />
            <t t-set="model" t-value="'res.company'" />
            <t t-set="state" t-value="state.get('account_onboarding_sale_tax_state')" />
        </t>
    </template>
    <record id="account_invoice_onboarding_sale_tax_form" model="ir.ui.view">
        <field name="name">account.invoice.layout.onboarding.form</field>
        <field name="model">res.company</field>
        <field name="mode">primary</field>
        <field name="priority">1000</field>
        <field name="arch" type="xml">
            <form>
                <div class="mb16">Choose a default sales tax for your products.</div>
                <label for="account_sale_tax_id" string="Sales Tax"/>
                <field name="account_sale_tax_id" />
                <footer>
                    <button string="Apply" class="btn btn-primary" type="object" name="action_save_onboarding_sale_tax" data-hotkey="q" />
                    <button string="Cancel" class="btn-secondary" special="cancel" data-hotkey="z" />
                </footer>
            </form>
        </field>
    </record>
    <record id="action_open_account_onboarding_sale_tax" model="ir.actions.act_window">
        <field name="name">Sales tax</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">res.company</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="account_invoice_onboarding_sale_tax_form" />
        <field name="target">new</field>
    </record>
</odoo>
