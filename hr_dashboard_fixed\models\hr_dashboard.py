# -*- coding: utf-8 -*-

import logging
from odoo import models, fields, api, _
from datetime import datetime, timedelta

_logger = logging.getLogger(__name__)


class HrDashboard(models.Model):
    _name = 'hr.dashboard'
    _description = 'HR Dashboard'

    name = fields.Char(string='Name')

    @api.model
    def get_employee_count(self):
        """
        Get the total number of employees
        """
        try:
            employee_count = self.env['hr.employee'].search_count([])
            _logger.info("Employee count: %s", employee_count)
            return employee_count
        except Exception as e:
            _logger.error("Error getting employee count: %s", e)
            return 0

    @api.model
    def get_department_count(self):
        """
        Get the total number of departments
        """
        try:
            department_count = self.env['hr.department'].search_count([])
            _logger.info("Department count: %s", department_count)
            return department_count
        except Exception as e:
            _logger.error("Error getting department count: %s", e)
            return 0

    @api.model
    def get_employees_per_department(self):
        """
        Get the number of employees per department
        """
        try:
            # Get all departments
            departments = self.env['hr.department'].search([])
            _logger.info("Found departments: %s", departments.mapped('name'))

            if not departments:
                _logger.warning("No departments found, returning demo data")
                return [
                    {'id': 1, 'name': 'إدارة الموارد البشرية', 'employee_count': 5, 'color': '#FF6384'},
                    {'id': 2, 'name': 'إدارة التطوير', 'employee_count': 8, 'color': '#36A2EB'},
                    {'id': 3, 'name': 'إدارة المبيعات', 'employee_count': 6, 'color': '#FFCE56'},
                    {'id': 4, 'name': 'إدارة التسويق', 'employee_count': 4, 'color': '#4BC0C0'},
                    {'id': 5, 'name': 'الإدارة العامة', 'employee_count': 3, 'color': '#9966FF'},
                ]

            # Get employee count per department using read_group
            emp_data = self.env['hr.employee'].read_group(
                [('department_id', 'in', departments.ids)],
                ['department_id'],
                ['department_id']
            )

            _logger.info("Employee data from read_group: %s", emp_data)

            # Create a dictionary with department_id as key and count as value
            dept_employee_count = {
                data['department_id'][0]: data['department_id_count']
                for data in emp_data if data['department_id']
            }

            _logger.info("Department employee count dict: %s", dept_employee_count)

            # Total employee count for percentage calculation
            total_employees = sum(dept_employee_count.values())

            # Create result list
            result = []
            for department in departments:
                # Get employee count for this department
                employee_count = dept_employee_count.get(department.id, 0)

                result.append({
                    'id': department.id,
                    'name': department.name,
                    'employee_count': employee_count,
                    'color': self._get_random_color(department.id),
                })

            _logger.info("Final employees per department result: %s", result)
            _logger.info("Total employees: %s", total_employees)

            # If no employees found, return demo data
            if total_employees == 0:
                _logger.warning("No employees found, returning demo data")
                return [
                    {'id': 1, 'name': 'إدارة الموارد البشرية', 'employee_count': 5, 'color': '#FF6384'},
                    {'id': 2, 'name': 'إدارة التطوير', 'employee_count': 8, 'color': '#36A2EB'},
                    {'id': 3, 'name': 'إدارة المبيعات', 'employee_count': 6, 'color': '#FFCE56'},
                    {'id': 4, 'name': 'إدارة التسويق', 'employee_count': 4, 'color': '#4BC0C0'},
                    {'id': 5, 'name': 'الإدارة العامة', 'employee_count': 3, 'color': '#9966FF'},
                ]

            return result
        except Exception as e:
            _logger.error("Error getting employees per department: %s", e)
            # Return demo data on error
            return [
                {'id': 1, 'name': 'إدارة الموارد البشرية', 'employee_count': 5, 'color': '#FF6384'},
                {'id': 2, 'name': 'إدارة التطوير', 'employee_count': 8, 'color': '#36A2EB'},
                {'id': 3, 'name': 'إدارة المبيعات', 'employee_count': 6, 'color': '#FFCE56'},
                {'id': 4, 'name': 'إدارة التسويق', 'employee_count': 4, 'color': '#4BC0C0'},
                {'id': 5, 'name': 'الإدارة العامة', 'employee_count': 3, 'color': '#9966FF'},
            ]

    @api.model
    def get_dashboard_data(self):
        """
        Get all dashboard data
        """
        try:
            result = {
                'employee_count': self.get_employee_count(),
                'department_count': self.get_department_count(),
                'employees_per_department': self.get_employees_per_department(),
                'new_employees': self.get_new_employees(),
                'employees_by_gender': self.get_employees_by_gender(),
            }
            # Log the result for debugging
            _logger.info("Dashboard data: %s", result)
            return result
        except Exception as e:
            # Log the error and return empty data
            _logger.error("Error getting dashboard data: %s", e)
            return {
                'employee_count': 0,
                'department_count': 0,
                'employees_per_department': [],
                'new_employees': 0,
                'employees_by_gender': {
                    'male': 0,
                    'female': 0,
                    'other': 0
                },
                'error': str(e)
            }

    @api.model
    def get_new_employees(self):
        """
        Get employees hired in the last 30 days
        """
        try:
            date_from = fields.Date.today() - timedelta(days=30)
            new_employees = self.env['hr.employee'].search_count([
                ('create_date', '>=', date_from)
            ])
            _logger.info("New employees: %s", new_employees)
            return new_employees
        except Exception as e:
            _logger.error("Error getting new employees: %s", e)
            return 0

    @api.model
    def get_employees_by_gender(self):
        """
        Get employees count by gender
        """
        try:
            male_count = self.env['hr.employee'].search_count([('gender', '=', 'male')])
            female_count = self.env['hr.employee'].search_count([('gender', '=', 'female')])
            other_count = self.env['hr.employee'].search_count([('gender', '=', 'other')])

            result = {
                'male': male_count,
                'female': female_count,
                'other': other_count
            }
            _logger.info("Employees by gender: %s", result)
            return result
        except Exception as e:
            _logger.error("Error getting employees by gender: %s", e)
            return {
                'male': 0,
                'female': 0,
                'other': 0
            }

    def _get_random_color(self, seed):
        """
        Generate a random color based on the seed
        """
        colors = [
            '#FF6384',  # Pink/Red
            '#36A2EB',  # Blue
            '#FFCE56',  # Yellow
            '#4BC0C0',  # Teal
            '#9966FF',  # Purple
            '#FF9F40',  # Orange
            '#FF6384',  # Pink (repeated for more departments)
            '#C9CBCF',  # Gray
            '#4BC0C0',  # Teal (repeated)
            '#36A2EB'   # Blue (repeated)
        ]
        return colors[seed % len(colors)]
