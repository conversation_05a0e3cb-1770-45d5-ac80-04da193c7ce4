# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_fleet
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON> (Quartile) <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:19+0000\n"
"Last-Translator: <PERSON><PERSON>, 2022\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_fleet
#: model:ir.model.fields,field_description:account_fleet.field_fleet_vehicle__account_move_ids
msgid "Account Move"
msgstr "会計仕訳"

#. module: account_fleet
#: model_terms:ir.ui.view,arch_db:account_fleet.fleet_vehicle_view_form
msgid "Bills"
msgstr "請求書"

#. module: account_fleet
#: model:ir.model.fields,field_description:account_fleet.field_fleet_vehicle__bill_count
msgid "Bills Count"
msgstr "請求件数"

#. module: account_fleet
#: model_terms:ir.ui.view,arch_db:account_fleet.account_move_view_tree
msgid "Creation Date"
msgstr "作成日"

#. module: account_fleet
#: model:ir.model,name:account_fleet.model_account_move
msgid "Journal Entry"
msgstr "仕訳"

#. module: account_fleet
#: model:ir.model,name:account_fleet.model_account_move_line
msgid "Journal Item"
msgstr "仕訳明細"

#. module: account_fleet
#: model:ir.model.fields,field_description:account_fleet.field_account_move_line__need_vehicle
msgid "Need Vehicle"
msgstr "車両が必要"

#. module: account_fleet
#: code:addons/account_fleet/models/account_move.py:0
#, python-format
msgid ""
"Service Vendor Bill: <a href=# data-oe-model=account.move data-oe-"
"id={move_id}>{move_name}</a>"
msgstr ""
"サービスベンダー請求: <a href=# data-oe-model=account.move data-oe-"
"id={move_id}>{move_name}</a>"

#. module: account_fleet
#: model:ir.model.fields,help:account_fleet.field_account_move_line__need_vehicle
msgid "Technical field to decide whether the vehicle_id field is editable"
msgstr "Vehicle_idフィールドが編集可能かどうかを判断するための機能的領域"

#. module: account_fleet
#: model:ir.model,name:account_fleet.model_fleet_vehicle
#: model:ir.model.fields,field_description:account_fleet.field_account_move_line__vehicle_id
msgid "Vehicle"
msgstr "車両"

#. module: account_fleet
#: model:fleet.service.type,name:account_fleet.data_fleet_service_type_vendor_bill
msgid "Vendor Bill"
msgstr "仕入先請求書"

#. module: account_fleet
#: model_terms:ir.ui.view,arch_db:account_fleet.fleet_vehicle_view_form
msgid "show the vendor bills for this vehicle"
msgstr "この車両のためのベンダーの請求書を表示する"
