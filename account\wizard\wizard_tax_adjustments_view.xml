<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="tax_adjustments_wizard" model="ir.ui.view">
        <field name="name">tax.adjustments.wizard.form</field>
        <field name="model">tax.adjustments.wizard</field>
        <field name="arch" type="xml">
        <form>
            <h1>
                <field name="reason" class="oe_inline" placeholder="Reason..."/>
            </h1>
            <group>
                <field name="report_id" invisible="1"/>
                <field name="tax_report_line_id" options="{'no_open': True, 'no_create': True}"/>
            </group>
            <group>
                <group>
                    <field name="amount"/>
                </group>
                <group>
                    <field name="adjustment_type"/>
                </group>
                <group string="Accounts">
                    <field name="debit_account_id"/>
                    <field name="credit_account_id"/>
                </group>
                <group string="Options">
                    <field name="journal_id"/>
                    <field name="date"/>
                </group>
            </group>
            <footer>
                <button name="create_move" string="Create and post move" type="object" default_focus="1" class="oe_highlight" data-hotkey="q"/>
                <button string="Cancel" class="btn btn-secondary" special="cancel" data-hotkey="z" />
            </footer>
        </form>
        </field>
    </record>
</odoo>
