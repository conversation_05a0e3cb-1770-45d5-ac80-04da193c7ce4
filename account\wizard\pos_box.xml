<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data>
        <record model="ir.ui.view" id="cash_box_out_form">
            <field name="name">cash_box_out</field>
            <field name="model">cash.box.out</field>
            <field name="arch" type="xml">
                <form string="Take Money In/Out">
                    <separator string="Describe why you put/take money from the cash register:"/>
                    <group>
                        <field name="name" class="oe_inline"/>
                        <field name="amount" class="oe_inline"/>
                    </group>

                    <footer>
                        <button name="run" string="Take Money In/Out" type="object" class="btn-primary" data-hotkey="q"/>
                        <button class="btn-secondary" special="cancel" data-hotkey="z" string="Cancel" />
                    </footer>
                </form>
            </field>
        </record>
    </data>
</odoo>
