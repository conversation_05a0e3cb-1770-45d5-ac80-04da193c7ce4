# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_lock
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# <PERSON> <<PERSON>.<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:19+0000\n"
"Last-Translator: <PERSON> <<PERSON>.<EMAIL>>, 2022\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"Any new All Users Lock Date must be posterior (or equal) to the previous "
"one."
msgstr "任何新的所有帳戶鎖定日期必須晚於 (或等於) 前一個."

#. module: account_lock
#: model:ir.model,name:account_lock.model_res_company
msgid "Companies"
msgstr "公司"

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid "The lock date for advisors is irreversible and can't be removed."
msgstr "鎖定日期不可刪除."

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid "The new tax lock date must be set after the previous lock date."
msgstr "必須在上一個鎖定日期之後設置新的稅務鎖定日期."

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid "The tax lock date is irreversible and can't be removed."
msgstr "稅務鎖定日期不可修改,無法刪除."

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"You cannot lock a period that has not yet ended. Therefore, the All Users "
"Lock Date must be anterior (or equal) to the last day of the previous month."
msgstr "您無法鎖定尚未結束的時間段. 因此，所有帳戶鎖定日期必須早於 (或等於) 上個月的最後一天."

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"You cannot lock a period that has not yet ended. Therefore, the tax lock "
"date must be anterior (or equal) to the last day of the previous month."
msgstr "您無法鎖定尚未結束的時間段. 因此，稅金鎖定日期必須早於 (或等於) 上個月的最後一天."

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"You cannot set stricter restrictions on advisors than on users. Therefore, "
"the All Users Lock Date must be anterior (or equal) to the Invoice/Bills "
"Lock Date."
msgstr "您不能對主辦會計設定比對帳戶更嚴格的限制. 因此，所有帳戶鎖定日期必須早於 (或等於) 應收/付賬單鎖定日期."
