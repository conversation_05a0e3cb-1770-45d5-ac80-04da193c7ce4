<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Chart of Accounts Templates -->

        <record id="view_account_chart_template_form" model="ir.ui.view">
            <field name="name">account.chart.template.form</field>
            <field name="model">account.chart.template</field>
            <field name="arch" type="xml">
                <form string="Chart of Accounts Template">
                    <group col="4">
                        <field name="name"/>
                        <field name="parent_id" />
                        <field name="bank_account_code_prefix"/>
                        <field name="cash_account_code_prefix"/>
                        <field name="transfer_account_code_prefix"/>
                        <field name="code_digits" />
                        <field name="visible" />
                        <field name="complete_tax_set" />
                    </group>
                    <separator string="Default Taxes" colspan="4"/>
                    <field name="tax_template_ids" colspan="4"  nolabel="1"/>
                    <separator string="Properties" colspan="4"/>
                    <group col="4">
                        <field name="property_account_receivable_id"/>
                        <field name="property_account_payable_id"/>
                        <field name="property_account_expense_categ_id"/>
                        <field name="property_account_income_categ_id"/>
                        <field name="property_account_expense_id"/>
                        <field name="property_account_income_id"/>
                    </group>
                </form>
            </field>
        </record>

        <record id="view_account_chart_template_seacrh" model="ir.ui.view">
            <field name="name">account.chart.template.search</field>
            <field name="model">account.chart.template</field>
            <field name="arch" type="xml">
                <search string="Search Chart of Account Templates">
                    <field name="name" string="Account Template"/>
                    <field name="bank_account_code_prefix"/>
                    <field name="cash_account_code_prefix"/>
                    <field name="transfer_account_code_prefix"/>
                    <group expand="0" string="Group By">
                        <filter string="Receivable Account" name="receivalbeacc" domain="[]" context="{'group_by':'property_account_receivable_id'}"/>
                        <filter string="Payable Account" name="payableacc" domain="[]" context="{'group_by':'property_account_payable_id'}"/>
                        <filter string="Income Account" name="incomeacc" domain="[]" context="{'group_by':'property_account_income_categ_id'}"/>
                        <filter string="Expense Account" name="expenseacc" domain="[]" context="{'group_by':'property_account_expense_categ_id'}"/>
                    </group>
                </search>
            </field>
        </record>

        <record id="view_account_chart_template_tree" model="ir.ui.view">
            <field name="name">account.chart.template.tree</field>
            <field name="model">account.chart.template</field>
            <field name="arch" type="xml">
                <tree string="Chart of Accounts Template">
                    <field name="name"/>
                    <field name="property_account_receivable_id" invisible="1"/>
                    <field name="property_account_payable_id" invisible="1"/>
                    <field name="property_account_expense_categ_id" invisible="1"/>
                    <field name="property_account_income_categ_id" invisible="1"/>
                </tree>
            </field>
        </record>
        <record id="action_account_chart_template_form" model="ir.actions.act_window">
            <field name="name">Chart of Accounts Templates</field>
            <field name="res_model">account.chart.template</field>
            <field name="view_mode">tree,form</field>
        </record>


        <!-- Account Templates -->

        <record id="view_account_template_form" model="ir.ui.view">
            <field name="name">account.account.template.form</field>
            <field name="model">account.account.template</field>
            <field name="arch" type="xml">
                <form string="Account Template">
                    <group col="4">
                        <field name="name"/>
                        <field name="code"/>
                        <newline/>
                        <field name="user_type_id" widget="account_hierarchy_selection"/>
                        <field name="currency_id" options="{'no_create': True}" groups="base.group_multi_currency"/>
                        <field name="tag_ids" domain="[('applicability', '=', 'accounts')]" widget="many2many_tags" context="{'default_applicability': 'accounts'}"/>
                        <field name="reconcile"/>
                        <field name="chart_template_id"/>
                    </group>
                    <separator string="Default Taxes"/>
                    <field name="tax_ids"/>
                    <separator string="Notes"/>
                    <field name="note" placeholder="Internal notes..."/>
                </form>
            </field>
        </record>

        <record id="view_account_template_tree" model="ir.ui.view">
            <field name="name">account.account.template.tree</field>
            <field name="model">account.account.template</field>
            <field name="arch" type="xml">
                <tree string="Account Template">
                    <field name="code"/>
                    <field name="name"/>
                    <field name="user_type_id" widget="account_hierarchy_selection" invisible="1"/>
                </tree>
            </field>
        </record>

        <record id="view_account_template_search" model="ir.ui.view">
            <field name="name">account.account.template.search</field>
            <field name="model">account.account.template</field>
            <field name="arch" type="xml">
                <search string="Search Account Templates">
                    <field name="name" filter_domain="['|', ('name','ilike',self), ('code','ilike',self)]" string="Account Template"/>
                    <field name="user_type_id"/>
                    <group expand="0" string="Group By">
                        <filter string="Account Type" name="accounttype" domain="[]" context="{'group_by':'user_type_id'}"/>
                   </group>
                </search>
            </field>
        </record>

        <!-- Account Tax Templates -->

        <record id="view_account_tax_template_form" model="ir.ui.view">
            <field name="name">account.tax.template.form</field>
            <field name="model">account.tax.template</field>
            <field name="arch" type="xml">
                <form string="Account Tax Template">
                    <sheet>
                        <group name="main_group">
                            <group>
                                <field name="name"/>
                            </group>
                            <group>
                                <field name="type_tax_use"/>
                            </group>
                        </group>
                        <notebook>
                            <page name="definition" string="Definition">
                                <group name="tax_definitions">
                                    <group>
                                        <field name="amount_type" />
                                        <label for="amount" attrs="{'invisible':[('amount_type','=', 'group')]}"/>
                                        <div attrs="{'invisible':[('amount_type','=', 'group')]}">
                                            <field name="amount" class="oe_inline" />
                                            <span class="o_form_label oe_inline" attrs="{'invisible':[('amount_type','=','fixed')]}">%</span>
                                        </div>
                                    </group>
                                </group>
                                <field name="children_tax_ids"
                                    attrs="{'invisible':['|', ('amount_type','!=','group'), ('type_tax_use','=','none')]}"
                                    domain="[('type_tax_use','in',('none',type_tax_use)), ('amount_type','!=','group')]">
                                    <tree string="Children Taxes">
                                        <field name="sequence" widget="handle" />
                                        <field name="name"/>
                                        <field name="amount_type" />
                                        <field name="amount" />
                                    </tree>
                                </field>
                            </page>
                            <page name="advanced_options" string="Advanced Options">
                                <group name="advanced_definitions">
                                    <group>
                                        <field name="description" attrs="{'invisible':[('amount_type','=', 'group')]}"/>
                                        <field name="analytic" attrs="{'invisible':[('amount_type','=', 'group')]}" groups="analytic.group_analytic_accounting" />
                                    </group>
                                    <group name="price_definitions">
                                        <field name="price_include" attrs="{'invisible':[('amount_type','=', 'group')]}" />
                                        <field name="include_base_amount" attrs="{'invisible':[('amount_type','=', 'group')]}" />
                                        <field name="is_base_affected"
                                               attrs="{'invisible': ['|', ('amount_type','=', 'group'), ('price_include', '=', True)]}"/>
                                    </group>
                                    <group name="tax_configuration">
                                        <field name="active" groups="base.group_no_one"/>
                                        <field name="tax_exigibility" widget="radio" attrs="{'invisible':[('amount_type','=', 'group')]}"/>
                                        <field name="cash_basis_transition_account_id" attrs="{'invisible': [('tax_exigibility', '=', 'on_invoice')], 'required': [('tax_exigibility', '=', 'on_payment')]}"/>
                                    </group>
                                </group>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="view_account_tax_template_tree" model="ir.ui.view">
            <field name="name">account.tax.template.tree</field>
            <field name="model">account.tax.template</field>
            <field name="arch" type="xml">
                <tree string="Account Tax Template">
                    <field name="name"/>
                    <field name="description"/>
                </tree>
            </field>
        </record>

        <record id="view_account_tax_template_search" model="ir.ui.view">
            <field name="name">account.tax.template.search</field>
            <field name="model">account.tax.template</field>
            <field name="arch" type="xml">
                <search string="Search Tax Templates">
                    <field name="name" filter_domain="['|', ('name', 'ilike', self), ('description', 'ilike', self)]" string="Tax Template"/>
                    <field name="chart_template_id"/>
                    <filter string="Sale" name="sale" domain="[('type_tax_use', '=', 'sale')]" help="Taxes used in Sales"/>
                    <filter string="Purchase" name="purchase" domain="[('type_tax_use', '=', 'purchase')]" help="Taxes used in Purchases"/>
                    <separator/>
                    <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                </search>
            </field>
        </record>

        <record id="action_account_tax_template_form" model="ir.actions.act_window">
            <field name="name">Tax Templates</field>
            <field name="res_model">account.tax.template</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="view_account_tax_template_search"/>
        </record>

         <!-- Fiscal Position Templates -->

        <record id="view_account_position_template_search" model="ir.ui.view">
            <field name="name">account.fiscal.position.template.search</field>
            <field name="model">account.fiscal.position.template</field>
            <field name="arch" type="xml">
                <search string="Fiscal Position">
                    <field name="name" string="Fiscal Position Template"/>
                </search>
            </field>
        </record>

        <record id="view_account_position_template_form" model="ir.ui.view">
            <field name="name">account.fiscal.position.template.form</field>
            <field name="model">account.fiscal.position.template</field>
            <field name="arch" type="xml">
                <form string="Fiscal Position Template">
                    <group col="4">
                        <field name="name"/>
                        <field name="chart_template_id"/>
                    </group>
                    <field name="tax_ids">
                        <tree string="Taxes Mapping" editable="bottom">
                            <field name="tax_src_id" domain="[('type_tax_use', '!=', None)]"/>
                            <field name="tax_dest_id" domain="[('type_tax_use', '!=', None)]"/>
                        </tree>
                        <form string="Taxes Mapping">
                            <field name="tax_src_id" domain="[('type_tax_use', '!=', None)]"/>
                            <field name="tax_dest_id" domain="[('type_tax_use', '!=', None)]"/>
                        </form>
                    </field>
                    <field name="account_ids">
                        <tree string="Accounts Mapping" editable="bottom">
                            <field name="account_src_id"/>
                            <field name="account_dest_id"/>
                        </tree>
                        <form string="Accounts Mapping">
                            <field name="account_src_id"/>
                            <field name="account_dest_id"/>
                        </form>
                    </field>
                </form>
            </field>
        </record>

        <record id="view_account_position_template_tree" model="ir.ui.view">
            <field name="name">account.fiscal.position.template.tree</field>
            <field name="model">account.fiscal.position.template</field>
            <field name="arch" type="xml">
                <tree string="Fiscal Position">
                    <field name="name"/>
                </tree>
            </field>
        </record>

    </data>
</odoo>
