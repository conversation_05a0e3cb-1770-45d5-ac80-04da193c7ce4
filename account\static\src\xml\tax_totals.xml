<?xml version="1.0" encoding="utf-8"?>
<templates>

    <t t-name="account.TaxGroupComponent" owl="1">
        <tr>
            <td class="o_td_label">
                <label class="o_form_label o_tax_total_label" t-esc="props.taxGroup.tax_group_name"/>
            </td>

            <td>
                <t t-if="allowTaxEdition">
                    <t t-if="['edit', 'disable'].includes(state.value)">
                        <span class="o_tax_group_edit_input">
                            <input
                                type="text"
                                t-ref="taxValueInput"
                                class="o_field_float
                                o_field_number o_input"
                                t-att-disabled="state.value === 'disable'"
                                t-on-blur.prevent="_onChangeTaxValue"/>
                        </span>
                    </t>
                    <t t-else="">
                        <span class="o_tax_group_edit" t-on-click.prevent="setState('edit')">
                            <i class="fa fa-pencil"/>
                            <span class="o_tax_group_amount_value">
                                <t t-esc="props.taxGroup.formatted_tax_group_amount"/>
                            </span>
                        </span>
                    </t>
                </t>
                <t t-else="">
                    <span class="o_tax_group_amount_value">
                        <t t-esc="props.taxGroup.formatted_tax_group_amount" style="white-space: nowrap;"/>
                    </span>
                </t>
            </td>
        </tr>
    </t>

    <div t-name="account.TaxTotalsField" owl="1">
        <table t-if="totals.value" class="oe_right">
            <tbody>
                <t t-foreach="totals.value.subtotals" t-as="subtotal" t-key="subtotal['name']">
                    <tr>
                        <td class="o_td_label">
                            <label class="o_form_label o_tax_total_label" t-esc="subtotal['name']"/>
                        </td>

                        <td>
                            <span t-att-name="subtotal['name']" style="white-space: nowrap; font-weight: bold;" t-esc="subtotal['formatted_amount']"/>
                        </td>
                    </tr>

                    <t t-foreach="totals.value.groups_by_subtotal[subtotal['name']]" t-as="taxGroup" t-key="taxGroup.group_key">
                        <TaxGroupComponent
                            taxGroup="taxGroup"
                            record="record"
                            allowTaxEdition="totals.value.allow_tax_edition"
                            t-on-change-tax-group="_onChangeTaxValueByTaxGroup"
                        />
                    </t>
                </t>

                <!-- Total amount with all taxes-->
                <tr>
                    <td class="o_td_label">
                        <label class="o_form_label o_tax_total_label">Total</label>
                    </td>

                    <td>
                        <span
                            name="amount_total"
                            t-att-class="Object.keys(totals.value.groups_by_subtotal).length > 0 ? 'oe_subtotal_footer_separator' : ''"
                            t-esc="totals.value.formatted_amount_total"
                            style="white-space: nowrap; font-weight: bold; font-size: 1.3em;"
                        />
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</templates>
