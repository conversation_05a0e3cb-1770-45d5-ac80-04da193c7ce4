# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_fleet
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:19+0000\n"
"Last-Translator: Linkup <<EMAIL>>, 2021\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_fleet
#: model:ir.model.fields,field_description:account_fleet.field_fleet_vehicle__account_move_ids
msgid "Account Move"
msgstr "회계 이동"

#. module: account_fleet
#: model_terms:ir.ui.view,arch_db:account_fleet.fleet_vehicle_view_form
msgid "Bills"
msgstr "청구서"

#. module: account_fleet
#: model:ir.model.fields,field_description:account_fleet.field_fleet_vehicle__bill_count
msgid "Bills Count"
msgstr "청구서 수"

#. module: account_fleet
#: model_terms:ir.ui.view,arch_db:account_fleet.account_move_view_tree
msgid "Creation Date"
msgstr "작성일자"

#. module: account_fleet
#: model:ir.model,name:account_fleet.model_account_move
msgid "Journal Entry"
msgstr "분개"

#. module: account_fleet
#: model:ir.model,name:account_fleet.model_account_move_line
msgid "Journal Item"
msgstr "분개 항목"

#. module: account_fleet
#: model:ir.model.fields,field_description:account_fleet.field_account_move_line__need_vehicle
msgid "Need Vehicle"
msgstr "차량 필요"

#. module: account_fleet
#: code:addons/account_fleet/models/account_move.py:0
#, python-format
msgid ""
"Service Vendor Bill: <a href=# data-oe-model=account.move data-oe-"
"id={move_id}>{move_name}</a>"
msgstr ""
"공급업체 청구서 서비스 : <a href=# data-oe-model=account.move data-oe-"
"id={move_id}>{move_name}</a>"

#. module: account_fleet
#: model:ir.model.fields,help:account_fleet.field_account_move_line__need_vehicle
msgid "Technical field to decide whether the vehicle_id field is editable"
msgstr "vehicle_id 필드의 수정가능 여부를 결정하는 기술필드입니다"

#. module: account_fleet
#: model:ir.model,name:account_fleet.model_fleet_vehicle
#: model:ir.model.fields,field_description:account_fleet.field_account_move_line__vehicle_id
msgid "Vehicle"
msgstr "차량"

#. module: account_fleet
#: model:fleet.service.type,name:account_fleet.data_fleet_service_type_vendor_bill
msgid "Vendor Bill"
msgstr "공급업체 청구서"

#. module: account_fleet
#: model_terms:ir.ui.view,arch_db:account_fleet.fleet_vehicle_view_form
msgid "show the vendor bills for this vehicle"
msgstr "이 차량의 공급업체 청구서 표시"
