<?xml version="1.0" encoding="UTF-8"?>

<odoo>
    <data>
        <record id="setup_financial_year_opening_form" model="ir.ui.view">
            <field name="name">account.financial.year.op.setup.wizard.form</field>
            <field name="model">account.financial.year.op</field>
            <field name="arch" type="xml">
                <form>
                    <sheet>
                        <group>
                            <group string="Fiscal Years">
                                <field name="opening_move_posted" invisible="1"/>
                                <field name="opening_date" attrs="{'readonly': [('opening_move_posted', '=', True)]}"/>

                                <label for="fiscalyear_last_month" string="Fiscal Year End"/>
                                <div>
                                    <field name="fiscalyear_last_day" class="oe_inline text-center" style="width: 20% !important;"/>
                                    <span style="width: 5%; display: inline-block"/>
                                    <field name="fiscalyear_last_month" class="oe_inline" style="width: 75% !important;"/>
                                </div>
                            </group>
                        </group>
                    </sheet>
                    <footer>
                        <button name="action_save_onboarding_fiscal_year" string="Apply"
                               class="oe_highlight" type="object" data-hotkey="q" />
                        <button special="cancel" data-hotkey="z" string="Cancel" />
                    </footer>
                </form>
            </field>
        </record>

        <record id="setup_bank_account_wizard" model="ir.ui.view">
            <field name="name">account.online.sync.res.partner.bank.setup.form</field>
            <field name="model">account.setup.bank.manual.config</field>
            <field name="arch" type="xml">
                <form>
                    <field name="num_journals_without_account" invisible="1"/>
                    <field name="journal_id" invisible="1"/>
                    <field name="company_id" invisible="1"/>
                    <field name="linked_journal_id" invisible="1"/>
                    <sheet>
                        <group>
                            <group>
                                <field name="acc_number" placeholder="e.g ****************"/>
                                <field name="bank_id" placeholder="e.g Bank of America"/>
                                <field name="bank_bic" placeholder="e.g GEBABEBB" string="Bank Identifier Code"/>
                            </group>
                        </group>
                        <group attrs="{'invisible': [('num_journals_without_account', '=', 0)]}">
                           <group>
                            <field name="linked_journal_id" options="{'no_create': True}"/>
                          </group>
                          <group>
                               <span class="text-muted">Leave empty to create a new journal for this bank account, or select a journal to link it with the bank account.</span>
                           </group>
                        </group>
                    </sheet>
                    <footer>
                        <button string="Create" class="oe_highlight" type="object" name="validate" data-hotkey="q"/>
                        <button string="Cancel" special="cancel" data-hotkey="z"/>
                    </footer>
                </form>
            </field>
        </record>

        <record id="init_accounts_tree" model="ir.ui.view">
            <field name="name">account.setup.opening.move.line.tree</field>
            <field name="model">account.account</field>
            <field name="arch" type="xml">
                <tree editable="top" create="1" delete="1" decoration-muted="opening_debit == 0 and opening_credit == 0">
                    <field name="code"/>
                    <field name="name"/>
                    <field name="company_id" invisible="1"/>
                    <field name="user_type_id" widget="account_hierarchy_selection"/>
                    <field name="reconcile" widget="boolean_toggle"/>
                    <field name="opening_debit"/>
                    <field name="opening_credit"/>
                    <field name="opening_balance" optional="hide"/>
                    <field name="tax_ids" optional="hide" widget="many2many_tags"/>
                    <field name="tag_ids" optional="hide" widget="many2many_tags"/>
                    <field name="allowed_journal_ids" optional="hide" widget="many2many_tags"/>
                    <button name="action_read_account" type="object" string="Setup" class="float-right btn-secondary"/>
                </tree>
            </field>
        </record>

    </data>
</odoo>
