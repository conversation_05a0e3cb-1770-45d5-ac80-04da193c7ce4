# -*- coding: utf-8 -*-
{
    'name': 'HR Dashboard Fixed',
    'version': '********.0',
    'summary': 'Dashboard for HR Module',
    'description': """
Dashboard for HR Module
----------------------
This module adds a dashboard to the HR module.
It displays statistics about employees and departments.
    """,
    'category': 'Human Resources',
    'author': 'Your Name',
    'website': 'https://www.example.com',
    'depends': [
        'hr',
        'web',
    ],
    'data': [
        'security/ir.model.access.csv',
        'views/hr_dashboard_views.xml',
    ],
    'assets': {
        'web.assets_backend': [
            'https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js',
            'hr_dashboard_fixed/static/src/js/hr_dashboard.js',
            'hr_dashboard_fixed/static/src/scss/hr_dashboard.scss',
        ],
        'web.assets_qweb': [
            'hr_dashboard_fixed/static/src/xml/hr_dashboard.xml',
        ],
    },
    'installable': True,
    'application': False,
    'auto_install': False,
    'license': 'LGPL-3',
}
